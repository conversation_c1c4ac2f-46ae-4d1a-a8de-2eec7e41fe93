{"version": "3.2.4", "results": [[":src/test/e2e/properties.spec.ts", {"duration": 0, "failed": true}], [":src/test/e2e/messaging.spec.ts", {"duration": 0, "failed": true}], [":src/test/integration/api-integration.test.ts", {"duration": 5218.336400000001, "failed": true}], [":src/test/e2e/performance.spec.ts", {"duration": 0, "failed": true}], [":src/test/e2e/realtime.spec.ts", {"duration": 0, "failed": true}], [":src/test/e2e/error-handling.spec.ts", {"duration": 0, "failed": true}], [":src/test/e2e/profile.spec.ts", {"duration": 0, "failed": true}], [":src/test/e2e/auth.spec.ts", {"duration": 0, "failed": true}]]}