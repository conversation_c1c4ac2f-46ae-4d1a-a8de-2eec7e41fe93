# LajoSpaces Backend Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:8080

# Database Configuration
MONGODB_URI=mongodb+srv://ezeokefranklin:<EMAIL>/lajospaces?retryWrites=true&w=majority
MONGODB_TEST_URI=mongodb+srv://ezeokefranklin:<EMAIL>/lajospaces_test?retryWrites=true&w=majority

# Redis Configuration
REDIS_URL=redis://default:<EMAIL>:17192

# JWT Configuration (Generate secure random strings)
JWT_SECRET=8f2a9c4e6b1d7f3a5e8c9b2d4f6a8c1e3b5d7f9a2c4e6b8d1f3a5c7e9b2d4f6a8c1e3b5d7f9a2c4e6b8d1f3a5c7e9b
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=3d7f9a2c4e6b8d1f5a7c9e2b4d6f8a1c3e5b7d9f2a4c6e8b1d3f5a7c9e2b4d6f8a1c3e5b7d9f2a4c6e8b1d3f5a7c
JWT_REFRESH_EXPIRES_IN=7d
PASSWORD_RESET_SECRET=9b2d4f6a8c1e3b5d7f9a2c4e6b8d1f3a5c7e9b2d4f6a8c1e3b5d7f9a2c4e6b8d1f3a5c7e9b2d4f6a8c1e3b5d7f
PASSWORD_RESET_EXPIRES_IN=1h

# Email Configuration (Zoho Mail)
SMTP_HOST=smtp.zoho.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=Q1zqAKsNCfG8
FROM_EMAIL=<EMAIL>
FROM_NAME=LajoSpaces

# File Upload Configuration (Cloudinary)
CLOUDINARY_CLOUD_NAME=dhq2flrlc
CLOUDINARY_API_KEY=778753662923711
CLOUDINARY_API_SECRET=tFLM_PzsgJSTzSQDtsWsleAQujY

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=7c9e2b4d6f8a1c3e5b7d9f2a4c6e8b1d3f5a7c9e2b4d6f8a1c3e5b7d9f2a4c6e8b1d3f5a7c9e2b4d6f8a1c3e5b7d
COOKIE_MAX_AGE=86400000

# API Configuration
API_VERSION=v1
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 🔒 SECURITY NOTES:
# 1. Never commit the actual .env file to Git
# 2. Generate strong, unique secrets for JWT tokens
# 3. Use app-specific passwords for email services
# 4. Keep database credentials secure
# 5. Use different secrets for development and production
