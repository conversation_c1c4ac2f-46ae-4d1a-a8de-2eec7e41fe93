ab8180c6caf9e12a15d7ea12866092cc
"use strict";

/* istanbul ignore next */
function cov_20oq5kwdlb() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\notificationService.ts";
  var hash = "44b6ee0dd5c68abfa2188b3d92731cf5a8837d1b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\notificationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 37
        }
      },
      "4": {
        start: {
          line: 7,
          column: 21
        },
        end: {
          line: 7,
          column: 52
        }
      },
      "5": {
        start: {
          line: 8,
          column: 26
        },
        end: {
          line: 8,
          column: 54
        }
      },
      "6": {
        start: {
          line: 9,
          column: 17
        },
        end: {
          line: 9,
          column: 43
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 10,
          column: 68
        }
      },
      "8": {
        start: {
          line: 11,
          column: 23
        },
        end: {
          line: 11,
          column: 65
        }
      },
      "9": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 79
        }
      },
      "10": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 19,
          column: 9
        }
      },
      "11": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 69
        }
      },
      "12": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 44
        }
      },
      "13": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 90,
          column: 9
        }
      },
      "14": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 34,
          column: 13
        }
      },
      "15": {
        start: {
          line: 29,
          column: 37
        },
        end: {
          line: 29,
          column: 103
        }
      },
      "16": {
        start: {
          line: 30,
          column: 16
        },
        end: {
          line: 33,
          column: 17
        }
      },
      "17": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 111
        }
      },
      "18": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 27
        }
      },
      "19": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 39,
          column: 14
        }
      },
      "20": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "21": {
        start: {
          line: 41,
          column: 16
        },
        end: {
          line: 41,
          column: 124
        }
      },
      "22": {
        start: {
          line: 42,
          column: 16
        },
        end: {
          line: 42,
          column: 23
        }
      },
      "23": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 48,
          column: 13
        }
      },
      "24": {
        start: {
          line: 46,
          column: 16
        },
        end: {
          line: 46,
          column: 88
        }
      },
      "25": {
        start: {
          line: 47,
          column: 16
        },
        end: {
          line: 47,
          column: 23
        }
      },
      "26": {
        start: {
          line: 50,
          column: 38
        },
        end: {
          line: 50,
          column: 120
        }
      },
      "27": {
        start: {
          line: 50,
          column: 83
        },
        end: {
          line: 50,
          column: 119
        }
      },
      "28": {
        start: {
          line: 51,
          column: 12
        },
        end: {
          line: 57,
          column: 13
        }
      },
      "29": {
        start: {
          line: 52,
          column: 28
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "30": {
        start: {
          line: 53,
          column: 16
        },
        end: {
          line: 56,
          column: 17
        }
      },
      "31": {
        start: {
          line: 54,
          column: 20
        },
        end: {
          line: 54,
          column: 88
        }
      },
      "32": {
        start: {
          line: 55,
          column: 20
        },
        end: {
          line: 55,
          column: 27
        }
      },
      "33": {
        start: {
          line: 59,
          column: 33
        },
        end: {
          line: 75,
          column: 13
        }
      },
      "34": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 77,
          column: 69
        }
      },
      "35": {
        start: {
          line: 79,
          column: 33
        },
        end: {
          line: 79,
          column: 82
        }
      },
      "36": {
        start: {
          line: 80,
          column: 34
        },
        end: {
          line: 82,
          column: 82
        }
      },
      "37": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 85,
          column: 13
        }
      },
      "38": {
        start: {
          line: 84,
          column: 16
        },
        end: {
          line: 84,
          column: 88
        }
      },
      "39": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 112
        }
      },
      "40": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 89,
          column: 80
        }
      },
      "41": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 119,
          column: 9
        }
      },
      "42": {
        start: {
          line: 97,
          column: 25
        },
        end: {
          line: 97,
          column: 132
        }
      },
      "43": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 100,
          column: 13
        }
      },
      "44": {
        start: {
          line: 99,
          column: 16
        },
        end: {
          line: 99,
          column: 23
        }
      },
      "45": {
        start: {
          line: 101,
          column: 33
        },
        end: {
          line: 113,
          column: 13
        }
      },
      "46": {
        start: {
          line: 114,
          column: 12
        },
        end: {
          line: 114,
          column: 64
        }
      },
      "47": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 78
        }
      },
      "48": {
        start: {
          line: 118,
          column: 12
        },
        end: {
          line: 118,
          column: 78
        }
      },
      "49": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 144,
          column: 9
        }
      },
      "50": {
        start: {
          line: 126,
          column: 25
        },
        end: {
          line: 126,
          column: 132
        }
      },
      "51": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 129,
          column: 13
        }
      },
      "52": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 23
        }
      },
      "53": {
        start: {
          line: 130,
          column: 33
        },
        end: {
          line: 138,
          column: 13
        }
      },
      "54": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 64
        }
      },
      "55": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 140,
          column: 89
        }
      },
      "56": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 143,
          column: 79
        }
      },
      "57": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 170,
          column: 9
        }
      },
      "58": {
        start: {
          line: 151,
          column: 26
        },
        end: {
          line: 153,
          column: 81
        }
      },
      "59": {
        start: {
          line: 154,
          column: 29
        },
        end: {
          line: 164,
          column: 14
        }
      },
      "60": {
        start: {
          line: 155,
          column: 16
        },
        end: {
          line: 162,
          column: 17
        }
      },
      "61": {
        start: {
          line: 156,
          column: 45
        },
        end: {
          line: 160,
          column: 21
        }
      },
      "62": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 161,
          column: 77
        }
      },
      "63": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 163,
          column: 41
        }
      },
      "64": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 47
        }
      },
      "65": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 166,
          column: 84
        }
      },
      "66": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 78
        }
      },
      "67": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 205,
          column: 9
        }
      },
      "68": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 181,
          column: 13
        }
      },
      "69": {
        start: {
          line: 179,
          column: 16
        },
        end: {
          line: 179,
          column: 77
        }
      },
      "70": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 180,
          column: 23
        }
      },
      "71": {
        start: {
          line: 182,
          column: 31
        },
        end: {
          line: 190,
          column: 13
        }
      },
      "72": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 199,
          column: 15
        }
      },
      "73": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 204,
          column: 77
        }
      },
      "74": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 265,
          column: 9
        }
      },
      "75": {
        start: {
          line: 213,
          column: 12
        },
        end: {
          line: 215,
          column: 13
        }
      },
      "76": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 214,
          column: 23
        }
      },
      "77": {
        start: {
          line: 216,
          column: 30
        },
        end: {
          line: 229,
          column: 13
        }
      },
      "78": {
        start: {
          line: 231,
          column: 27
        },
        end: {
          line: 252,
          column: 14
        }
      },
      "79": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 261,
          column: 13
        }
      },
      "80": {
        start: {
          line: 254,
          column: 16
        },
        end: {
          line: 257,
          column: 19
        }
      },
      "81": {
        start: {
          line: 260,
          column: 16
        },
        end: {
          line: 260,
          column: 107
        }
      },
      "82": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 78
        }
      },
      "83": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 273,
          column: 9
        }
      },
      "84": {
        start: {
          line: 272,
          column: 12
        },
        end: {
          line: 272,
          column: 24
        }
      },
      "85": {
        start: {
          line: 274,
          column: 25
        },
        end: {
          line: 274,
          column: 50
        }
      },
      "86": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 284,
          column: 9
        }
      },
      "87": {
        start: {
          line: 277,
          column: 16
        },
        end: {
          line: 277,
          column: 51
        }
      },
      "88": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 50
        }
      },
      "89": {
        start: {
          line: 281,
          column: 16
        },
        end: {
          line: 281,
          column: 49
        }
      },
      "90": {
        start: {
          line: 283,
          column: 16
        },
        end: {
          line: 283,
          column: 28
        }
      },
      "91": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 292,
          column: 9
        }
      },
      "92": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 25
        }
      },
      "93": {
        start: {
          line: 293,
          column: 30
        },
        end: {
          line: 293,
          column: 61
        }
      },
      "94": {
        start: {
          line: 294,
          column: 8
        },
        end: {
          line: 303,
          column: 9
        }
      },
      "95": {
        start: {
          line: 296,
          column: 16
        },
        end: {
          line: 296,
          column: 56
        }
      },
      "96": {
        start: {
          line: 298,
          column: 16
        },
        end: {
          line: 298,
          column: 55
        }
      },
      "97": {
        start: {
          line: 300,
          column: 16
        },
        end: {
          line: 300,
          column: 54
        }
      },
      "98": {
        start: {
          line: 302,
          column: 16
        },
        end: {
          line: 302,
          column: 29
        }
      },
      "99": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 314,
          column: 9
        }
      },
      "100": {
        start: {
          line: 310,
          column: 12
        },
        end: {
          line: 310,
          column: 60
        }
      },
      "101": {
        start: {
          line: 313,
          column: 12
        },
        end: {
          line: 313,
          column: 57
        }
      },
      "102": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 337,
          column: 9
        }
      },
      "103": {
        start: {
          line: 322,
          column: 16
        },
        end: {
          line: 324,
          column: 38
        }
      },
      "104": {
        start: {
          line: 326,
          column: 16
        },
        end: {
          line: 326,
          column: 41
        }
      },
      "105": {
        start: {
          line: 328,
          column: 16
        },
        end: {
          line: 328,
          column: 40
        }
      },
      "106": {
        start: {
          line: 330,
          column: 16
        },
        end: {
          line: 330,
          column: 44
        }
      },
      "107": {
        start: {
          line: 332,
          column: 16
        },
        end: {
          line: 332,
          column: 46
        }
      },
      "108": {
        start: {
          line: 334,
          column: 16
        },
        end: {
          line: 334,
          column: 39
        }
      },
      "109": {
        start: {
          line: 336,
          column: 16
        },
        end: {
          line: 336,
          column: 40
        }
      },
      "110": {
        start: {
          line: 343,
          column: 24
        },
        end: {
          line: 343,
          column: 75
        }
      },
      "111": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 353,
          column: 9
        }
      },
      "112": {
        start: {
          line: 346,
          column: 16
        },
        end: {
          line: 346,
          column: 82
        }
      },
      "113": {
        start: {
          line: 348,
          column: 16
        },
        end: {
          line: 348,
          column: 44
        }
      },
      "114": {
        start: {
          line: 350,
          column: 16
        },
        end: {
          line: 350,
          column: 50
        }
      },
      "115": {
        start: {
          line: 352,
          column: 16
        },
        end: {
          line: 352,
          column: 31
        }
      },
      "116": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 359,
          column: 49
        }
      },
      "117": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 374,
          column: 9
        }
      },
      "118": {
        start: {
          line: 367,
          column: 16
        },
        end: {
          line: 367,
          column: 37
        }
      },
      "119": {
        start: {
          line: 369,
          column: 16
        },
        end: {
          line: 369,
          column: 35
        }
      },
      "120": {
        start: {
          line: 371,
          column: 16
        },
        end: {
          line: 371,
          column: 45
        }
      },
      "121": {
        start: {
          line: 373,
          column: 16
        },
        end: {
          line: 373,
          column: 46
        }
      },
      "122": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 390,
          column: 9
        }
      },
      "123": {
        start: {
          line: 383,
          column: 12
        },
        end: {
          line: 383,
          column: 70
        }
      },
      "124": {
        start: {
          line: 385,
          column: 27
        },
        end: {
          line: 385,
          column: 113
        }
      },
      "125": {
        start: {
          line: 386,
          column: 12
        },
        end: {
          line: 386,
          column: 92
        }
      },
      "126": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 389,
          column: 77
        }
      },
      "127": {
        start: {
          line: 396,
          column: 8
        },
        end: {
          line: 426,
          column: 9
        }
      },
      "128": {
        start: {
          line: 398,
          column: 64
        },
        end: {
          line: 406,
          column: 14
        }
      },
      "129": {
        start: {
          line: 407,
          column: 30
        },
        end: {
          line: 410,
          column: 18
        }
      },
      "130": {
        start: {
          line: 408,
          column: 16
        },
        end: {
          line: 408,
          column: 43
        }
      },
      "131": {
        start: {
          line: 409,
          column: 16
        },
        end: {
          line: 409,
          column: 27
        }
      },
      "132": {
        start: {
          line: 411,
          column: 12
        },
        end: {
          line: 416,
          column: 14
        }
      },
      "133": {
        start: {
          line: 419,
          column: 12
        },
        end: {
          line: 419,
          column: 78
        }
      },
      "134": {
        start: {
          line: 420,
          column: 12
        },
        end: {
          line: 425,
          column: 14
        }
      },
      "135": {
        start: {
          line: 429,
          column: 0
        },
        end: {
          line: 429,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 13,
            column: 4
          },
          end: {
            line: 13,
            column: 5
          }
        },
        loc: {
          start: {
            line: 13,
            column: 18
          },
          end: {
            line: 15,
            column: 5
          }
        },
        line: 13
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 16,
            column: 4
          },
          end: {
            line: 16,
            column: 5
          }
        },
        loc: {
          start: {
            line: 16,
            column: 25
          },
          end: {
            line: 21,
            column: 5
          }
        },
        line: 16
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        loc: {
          start: {
            line: 25,
            column: 70
          },
          end: {
            line: 91,
            column: 5
          }
        },
        line: 25
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 50,
            column: 75
          },
          end: {
            line: 50,
            column: 76
          }
        },
        loc: {
          start: {
            line: 50,
            column: 83
          },
          end: {
            line: 50,
            column: 119
          }
        },
        line: 50
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        },
        loc: {
          start: {
            line: 95,
            column: 51
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 95
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 124,
            column: 5
          }
        },
        loc: {
          start: {
            line: 124,
            column: 60
          },
          end: {
            line: 145,
            column: 5
          }
        },
        line: 124
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 149,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        },
        loc: {
          start: {
            line: 149,
            column: 55
          },
          end: {
            line: 171,
            column: 5
          }
        },
        line: 149
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 154,
            column: 39
          },
          end: {
            line: 154,
            column: 40
          }
        },
        loc: {
          start: {
            line: 154,
            column: 47
          },
          end: {
            line: 164,
            column: 13
          }
        },
        line: 154
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        },
        loc: {
          start: {
            line: 175,
            column: 51
          },
          end: {
            line: 206,
            column: 5
          }
        },
        line: 175
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 210,
            column: 5
          }
        },
        loc: {
          start: {
            line: 210,
            column: 66
          },
          end: {
            line: 266,
            column: 5
          }
        },
        line: 210
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 5
          }
        },
        loc: {
          start: {
            line: 270,
            column: 39
          },
          end: {
            line: 285,
            column: 5
          }
        },
        line: 270
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 289,
            column: 4
          },
          end: {
            line: 289,
            column: 5
          }
        },
        loc: {
          start: {
            line: 289,
            column: 44
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 289
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        loc: {
          start: {
            line: 308,
            column: 54
          },
          end: {
            line: 315,
            column: 5
          }
        },
        line: 308
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 319,
            column: 5
          }
        },
        loc: {
          start: {
            line: 319,
            column: 40
          },
          end: {
            line: 338,
            column: 5
          }
        },
        line: 319
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 342,
            column: 4
          },
          end: {
            line: 342,
            column: 5
          }
        },
        loc: {
          start: {
            line: 342,
            column: 33
          },
          end: {
            line: 354,
            column: 5
          }
        },
        line: 342
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 358,
            column: 5
          }
        },
        loc: {
          start: {
            line: 358,
            column: 31
          },
          end: {
            line: 360,
            column: 5
          }
        },
        line: 358
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 364,
            column: 4
          },
          end: {
            line: 364,
            column: 5
          }
        },
        loc: {
          start: {
            line: 364,
            column: 27
          },
          end: {
            line: 375,
            column: 5
          }
        },
        line: 364
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 379,
            column: 4
          },
          end: {
            line: 379,
            column: 5
          }
        },
        loc: {
          start: {
            line: 379,
            column: 40
          },
          end: {
            line: 391,
            column: 5
          }
        },
        line: 379
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        },
        loc: {
          start: {
            line: 395,
            column: 39
          },
          end: {
            line: 427,
            column: 5
          }
        },
        line: 395
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 407,
            column: 44
          },
          end: {
            line: 407,
            column: 45
          }
        },
        loc: {
          start: {
            line: 407,
            column: 59
          },
          end: {
            line: 410,
            column: 13
          }
        },
        line: 407
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 19,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 19,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 34,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 34,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "5": {
        loc: {
          start: {
            line: 30,
            column: 16
          },
          end: {
            line: 33,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 16
          },
          end: {
            line: 33,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "6": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 110
          }
        }],
        line: 30
      },
      "7": {
        loc: {
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "8": {
        loc: {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 40,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 40,
            column: 26
          }
        }, {
          start: {
            line: 40,
            column: 30
          },
          end: {
            line: 40,
            column: 37
          }
        }],
        line: 40
      },
      "9": {
        loc: {
          start: {
            line: 45,
            column: 12
          },
          end: {
            line: 48,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 12
          },
          end: {
            line: 48,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "10": {
        loc: {
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 57,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 57,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "11": {
        loc: {
          start: {
            line: 53,
            column: 16
          },
          end: {
            line: 56,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 16
          },
          end: {
            line: 56,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "12": {
        loc: {
          start: {
            line: 53,
            column: 20
          },
          end: {
            line: 53,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 20
          },
          end: {
            line: 53,
            column: 49
          }
        }, {
          start: {
            line: 53,
            column: 53
          },
          end: {
            line: 53,
            column: 87
          }
        }],
        line: 53
      },
      "13": {
        loc: {
          start: {
            line: 80,
            column: 34
          },
          end: {
            line: 82,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 34
          },
          end: {
            line: 80,
            column: 47
          }
        }, {
          start: {
            line: 81,
            column: 17
          },
          end: {
            line: 81,
            column: 50
          }
        }, {
          start: {
            line: 82,
            column: 20
          },
          end: {
            line: 82,
            column: 81
          }
        }],
        line: 80
      },
      "14": {
        loc: {
          start: {
            line: 83,
            column: 12
          },
          end: {
            line: 85,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 12
          },
          end: {
            line: 85,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 83
      },
      "15": {
        loc: {
          start: {
            line: 83,
            column: 16
          },
          end: {
            line: 83,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 16
          },
          end: {
            line: 83,
            column: 29
          }
        }, {
          start: {
            line: 83,
            column: 33
          },
          end: {
            line: 83,
            column: 87
          }
        }],
        line: 83
      },
      "16": {
        loc: {
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 100,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 100,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "17": {
        loc: {
          start: {
            line: 98,
            column: 16
          },
          end: {
            line: 98,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 16
          },
          end: {
            line: 98,
            column: 21
          }
        }, {
          start: {
            line: 98,
            column: 25
          },
          end: {
            line: 98,
            column: 68
          }
        }],
        line: 98
      },
      "18": {
        loc: {
          start: {
            line: 127,
            column: 12
          },
          end: {
            line: 129,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 12
          },
          end: {
            line: 129,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "19": {
        loc: {
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 127,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 127,
            column: 21
          }
        }, {
          start: {
            line: 127,
            column: 25
          },
          end: {
            line: 127,
            column: 69
          }
        }],
        line: 127
      },
      "20": {
        loc: {
          start: {
            line: 155,
            column: 16
          },
          end: {
            line: 162,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 16
          },
          end: {
            line: 162,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "21": {
        loc: {
          start: {
            line: 178,
            column: 12
          },
          end: {
            line: 181,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 12
          },
          end: {
            line: 181,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "22": {
        loc: {
          start: {
            line: 178,
            column: 16
          },
          end: {
            line: 178,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 178,
            column: 16
          },
          end: {
            line: 178,
            column: 32
          }
        }, {
          start: {
            line: 178,
            column: 36
          },
          end: {
            line: 178,
            column: 64
          }
        }],
        line: 178
      },
      "23": {
        loc: {
          start: {
            line: 213,
            column: 12
          },
          end: {
            line: 215,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 12
          },
          end: {
            line: 215,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "24": {
        loc: {
          start: {
            line: 238,
            column: 12
          },
          end: {
            line: 238,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 238,
            column: 39
          },
          end: {
            line: 238,
            column: 102
          }
        }, {
          start: {
            line: 238,
            column: 105
          },
          end: {
            line: 238,
            column: 107
          }
        }],
        line: 238
      },
      "25": {
        loc: {
          start: {
            line: 248,
            column: 12
          },
          end: {
            line: 248,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 248,
            column: 39
          },
          end: {
            line: 248,
            column: 82
          }
        }, {
          start: {
            line: 248,
            column: 85
          },
          end: {
            line: 248,
            column: 87
          }
        }],
        line: 248
      },
      "26": {
        loc: {
          start: {
            line: 253,
            column: 12
          },
          end: {
            line: 261,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 12
          },
          end: {
            line: 261,
            column: 13
          }
        }, {
          start: {
            line: 259,
            column: 17
          },
          end: {
            line: 261,
            column: 13
          }
        }],
        line: 253
      },
      "27": {
        loc: {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "28": {
        loc: {
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 276,
            column: 12
          },
          end: {
            line: 277,
            column: 51
          }
        }, {
          start: {
            line: 278,
            column: 12
          },
          end: {
            line: 279,
            column: 50
          }
        }, {
          start: {
            line: 280,
            column: 12
          },
          end: {
            line: 281,
            column: 49
          }
        }, {
          start: {
            line: 282,
            column: 12
          },
          end: {
            line: 283,
            column: 28
          }
        }],
        line: 275
      },
      "29": {
        loc: {
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "30": {
        loc: {
          start: {
            line: 294,
            column: 8
          },
          end: {
            line: 303,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 295,
            column: 12
          },
          end: {
            line: 296,
            column: 56
          }
        }, {
          start: {
            line: 297,
            column: 12
          },
          end: {
            line: 298,
            column: 55
          }
        }, {
          start: {
            line: 299,
            column: 12
          },
          end: {
            line: 300,
            column: 54
          }
        }, {
          start: {
            line: 301,
            column: 12
          },
          end: {
            line: 302,
            column: 29
          }
        }],
        line: 294
      },
      "31": {
        loc: {
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 314,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 314,
            column: 9
          }
        }, {
          start: {
            line: 312,
            column: 13
          },
          end: {
            line: 314,
            column: 9
          }
        }],
        line: 309
      },
      "32": {
        loc: {
          start: {
            line: 313,
            column: 19
          },
          end: {
            line: 313,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 19
          },
          end: {
            line: 313,
            column: 37
          }
        }, {
          start: {
            line: 313,
            column: 41
          },
          end: {
            line: 313,
            column: 56
          }
        }],
        line: 313
      },
      "33": {
        loc: {
          start: {
            line: 320,
            column: 8
          },
          end: {
            line: 337,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 321,
            column: 12
          },
          end: {
            line: 324,
            column: 38
          }
        }, {
          start: {
            line: 325,
            column: 12
          },
          end: {
            line: 326,
            column: 41
          }
        }, {
          start: {
            line: 327,
            column: 12
          },
          end: {
            line: 328,
            column: 40
          }
        }, {
          start: {
            line: 329,
            column: 12
          },
          end: {
            line: 330,
            column: 44
          }
        }, {
          start: {
            line: 331,
            column: 12
          },
          end: {
            line: 332,
            column: 46
          }
        }, {
          start: {
            line: 333,
            column: 12
          },
          end: {
            line: 334,
            column: 39
          }
        }, {
          start: {
            line: 335,
            column: 12
          },
          end: {
            line: 336,
            column: 40
          }
        }],
        line: 320
      },
      "34": {
        loc: {
          start: {
            line: 322,
            column: 23
          },
          end: {
            line: 324,
            column: 37
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 323,
            column: 22
          },
          end: {
            line: 323,
            column: 63
          }
        }, {
          start: {
            line: 324,
            column: 22
          },
          end: {
            line: 324,
            column: 37
          }
        }],
        line: 322
      },
      "35": {
        loc: {
          start: {
            line: 343,
            column: 24
          },
          end: {
            line: 343,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 343,
            column: 24
          },
          end: {
            line: 343,
            column: 48
          }
        }, {
          start: {
            line: 343,
            column: 52
          },
          end: {
            line: 343,
            column: 75
          }
        }],
        line: 343
      },
      "36": {
        loc: {
          start: {
            line: 344,
            column: 8
          },
          end: {
            line: 353,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 346,
            column: 82
          }
        }, {
          start: {
            line: 347,
            column: 12
          },
          end: {
            line: 348,
            column: 44
          }
        }, {
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 350,
            column: 50
          }
        }, {
          start: {
            line: 351,
            column: 12
          },
          end: {
            line: 352,
            column: 31
          }
        }],
        line: 344
      },
      "37": {
        loc: {
          start: {
            line: 365,
            column: 8
          },
          end: {
            line: 374,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 366,
            column: 12
          },
          end: {
            line: 367,
            column: 37
          }
        }, {
          start: {
            line: 368,
            column: 12
          },
          end: {
            line: 369,
            column: 35
          }
        }, {
          start: {
            line: 370,
            column: 12
          },
          end: {
            line: 371,
            column: 45
          }
        }, {
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 373,
            column: 46
          }
        }],
        line: 365
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0, 0, 0],
      "29": [0, 0],
      "30": [0, 0, 0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0, 0, 0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0],
      "37": [0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\notificationService.ts",
      mappings: ";;;;;;AAAA,qDAA4C;AAC5C,uDAAoD;AACpD,4CAAyC;AACzC,qEAEsC;AACtC,kEAA0C;AAgC1C,MAAa,mBAAmB;IAI9B;QACE,IAAI,CAAC,eAAe,GAAG,iCAAe,CAAC,WAAW,EAAE,CAAC;IACvD,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YAClC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,uBAAuB,CAClC,OAAY,EACZ,YAAiB,EACjB,WAAmB;QAEnB,IAAI,CAAC;YACH,gFAAgF;YAChF,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,eAAe,CAAC;gBACxF,IAAI,YAAY,EAAE,IAAI,KAAK,WAAW,IAAI,YAAY,CAAC,OAAO,KAAK,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;oBAC/F,eAAM,CAAC,KAAK,CAAC,6BAA6B,WAAW,2BAA2B,CAAC,CAAC;oBAClF,OAAO;gBACT,CAAC;YACH,CAAC;YAED,gCAAgC;YAChC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC5C,iBAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,0DAA0D,CAAC;gBAC7F,iBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC;aACpE,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC,SAAS,aAAa,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;gBACnG,OAAO;YACT,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;gBACvD,eAAM,CAAC,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;gBAC/D,OAAO;YACT,CAAC;YAED,iCAAiC;YACjC,MAAM,iBAAiB,GAAG,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAC5D,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,WAAW,CAClD,CAAC;YAEF,IAAI,iBAAiB,EAAE,OAAO,EAAE,CAAC;gBAC/B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,iBAAiB,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;oBACxE,eAAM,CAAC,KAAK,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;oBAC3D,OAAO;gBACT,CAAC;YACH,CAAC;YAED,8BAA8B;YAC9B,MAAM,YAAY,GAAwB;gBACxC,EAAE,EAAE,OAAO,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACtC,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,YAAY,CAAC;gBAC7D,IAAI,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;gBAC9C,IAAI,EAAE;oBACJ,cAAc,EAAE,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE;oBAC3C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACjC,QAAQ,EAAG,MAAc,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACxC,UAAU,EAAE,GAAI,MAAc,CAAC,SAAS,IAAK,MAAc,CAAC,QAAQ,EAAE;oBACtE,YAAY,EAAG,MAAc,CAAC,MAAM;oBACpC,WAAW,EAAE,OAAO,CAAC,WAAW;iBACjC;gBACD,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;aAClE,CAAC;YAEF,yBAAyB;YACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAEzD,sEAAsE;YACtE,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACvE,MAAM,aAAa,GAAG,CAAC,YAAY;gBACjC,CAAC,YAAY,CAAC,MAAM,KAAK,SAAS;oBACjC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAElE,IAAI,aAAa,IAAI,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC5E,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YAC1E,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,qCAAqC,WAAW,gBAAgB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAE7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB,CAChC,MAAc,EACd,SAAc;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,iBAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,0DAA0D,CAAC,CAAC;YAE5G,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBACzD,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAwB;gBACxC,EAAE,EAAE,SAAS,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACzC,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,eAAe;gBACtB,IAAI,EAAE,kBAAkB,SAAS,CAAC,IAAI,eAAe,SAAS,CAAC,kBAAkB,kBAAkB;gBACnG,IAAI,EAAE;oBACJ,OAAO,EAAE,SAAS,CAAC,EAAE;oBACrB,UAAU,EAAE,SAAS,CAAC,IAAI;oBAC1B,kBAAkB,EAAE,SAAS,CAAC,kBAAkB;iBACjD;gBACD,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAEpD,eAAM,CAAC,IAAI,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CACjC,MAAc,EACd,KAAa,EACb,IAAY,EACZ,IAAU;QAEV,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,iBAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,0DAA0D,CAAC,CAAC;YAE5G,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC1D,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAwB;gBACxC,EAAE,EAAE,UAAU,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACpC,IAAI,EAAE,QAAQ;gBACd,KAAK;gBACL,IAAI;gBACJ,IAAI;gBACJ,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAEpD,eAAM,CAAC,IAAI,CAAC,oCAAoC,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC;QAEtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB,CAChC,OAAiB,EACjB,YAA2D;QAE3D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,iBAAI,CAAC,IAAI,CAAC;gBAC5B,GAAG,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE;aACtB,CAAC,CAAC,MAAM,CAAC,0DAA0D,CAAC,CAAC;YAEtE,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzD,MAAM,gBAAgB,GAAwB;wBAC5C,GAAG,YAAY;wBACf,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;wBACpC,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;oBACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBAC3D,CAAC;gBACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEnC,eAAM,CAAC,IAAI,CAAC,6BAA6B,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;QAEjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,IAAS,EACT,YAAiC;QAEjC,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrD,eAAM,CAAC,KAAK,CAAC,2BAA2B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,MAAM,UAAU,GAA2B;gBACzC,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,IAAI,EAAE,yBAAyB;gBAC/B,KAAK,EAAE,wBAAwB;gBAC/B,KAAK,EAAE,SAAS;gBAChB,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;gBAC9C,IAAI,EAAE,YAAY,CAAC,IAAI;aACxB,CAAC;YAEF,+DAA+D;YAC/D,mFAAmF;YAEnF,sCAAsC;YACtC,eAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,CAAC,UAAU,CAAC,MAAM,WAAW,EAAE;gBACnF,MAAM,EAAE,IAAI,CAAC,GAAG;gBAChB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,IAAI,EAAE,YAAY,CAAC,IAAI;aACxB,CAAC,CAAC;YAEH,mDAAmD;YACnD,mEAAmE;QAErE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,IAAS,EACT,YAAiC,EACjC,YAAkB;QAElB,IAAI,CAAC;YACH,gDAAgD;YAChD,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/D,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG;gBAChB,EAAE,EAAE,IAAI,CAAC,KAAK;gBACd,OAAO,EAAE,YAAY,CAAC,KAAK;gBAC3B,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC;gBAClD,IAAI,EAAE;oBACJ,QAAQ,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAC9C,iBAAiB,EAAE,YAAY,CAAC,KAAK;oBACrC,gBAAgB,EAAE,YAAY,CAAC,IAAI;oBACnC,iBAAiB,EAAE,YAAY,EAAE,KAAK;oBACtC,UAAU,EAAE,YAAY,CAAC,IAAI,EAAE,UAAU;oBACzC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;oBAC1C,cAAc,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,iCAAiC,IAAI,CAAC,GAAG,EAAE;iBACvF;aACF,CAAC;YAEF,qCAAqC;YACrC,MAAM,MAAM,GAAG,MAAM,sBAAY,CAAC,SAAS,CAAC;gBAC1C,EAAE,EAAE,IAAI,CAAC,KAAK;gBACd,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,IAAI,EAAE;gBACE,SAAS,CAAC,IAAI,CAAC,iBAAiB;qBAC3B,SAAS,CAAC,IAAI,CAAC,QAAQ;eAC7B,SAAS,CAAC,IAAI,CAAC,gBAAgB;YAClC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,SAAS,CAAC,IAAI,CAAC,SAAS,wBAAwB,CAAC,CAAC,CAAC,EAAE;wBACnF,SAAS,CAAC,IAAI,CAAC,cAAc;SAC5C;gBACD,IAAI,EAAE;YACF,SAAS,CAAC,IAAI,CAAC,iBAAiB;;kBAE1B,SAAS,CAAC,IAAI,CAAC,QAAQ;;YAE7B,SAAS,CAAC,IAAI,CAAC,gBAAgB;;YAE/B,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;;yBAE9D,SAAS,CAAC,IAAI,CAAC,cAAc;SAC7C;aACF,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,KAAK,GAAG,EAAE;oBACvD,OAAO,EAAE,SAAS,CAAC,OAAO;oBAC1B,SAAS,EAAE,MAAM,CAAC,SAAS;iBAC5B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,wCAAwC,IAAI,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YACpF,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,IAAS,EAAE,IAAY;QACpD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,CAAC,qBAAqB;QACpC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAE3C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,QAAQ,KAAK,KAAK,CAAC;YACrC,KAAK,OAAO;gBACV,OAAO,QAAQ,CAAC,OAAO,KAAK,KAAK,CAAC;YACpC,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC;YACnC;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,IAAS,EAAE,IAAY;QACzD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAEtD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,aAAa,CAAC,QAAQ,KAAK,KAAK,CAAC;YAC1C,KAAK,OAAO;gBACV,OAAO,aAAa,CAAC,OAAO,KAAK,KAAK,CAAC;YACzC,KAAK,QAAQ;gBACX,OAAO,aAAa,CAAC,MAAM,KAAK,KAAK,CAAC;YACxC;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,MAAW,EAAE,YAAiB;QAChE,IAAI,YAAY,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YAC/C,OAAO,GAAG,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,OAAO,YAAY,CAAC,KAAK,IAAI,eAAe,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,OAAY;QAC7C,QAAQ,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,KAAK,MAAM;gBACT,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;oBACjC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;oBAC3C,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YACtB,KAAK,OAAO;gBACV,OAAO,iBAAiB,CAAC;YAC3B,KAAK,MAAM;gBACT,OAAO,gBAAgB,CAAC;YAC1B,KAAK,UAAU;gBACb,OAAO,oBAAoB,CAAC;YAC9B,KAAK,gBAAgB;gBACnB,OAAO,sBAAsB,CAAC;YAChC,KAAK,QAAQ;gBACX,OAAO,OAAO,CAAC,OAAO,CAAC;YACzB;gBACE,OAAO,gBAAgB,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,YAAiC;QACtD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB,CAAC;QAEpE,QAAQ,YAAY,CAAC,IAAI,EAAE,CAAC;YAC1B,KAAK,SAAS;gBACZ,OAAO,GAAG,OAAO,aAAa,YAAY,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC;YACpE,KAAK,OAAO;gBACV,OAAO,GAAG,OAAO,UAAU,CAAC;YAC9B,KAAK,QAAQ;gBACX,OAAO,GAAG,OAAO,gBAAgB,CAAC;YACpC;gBACE,OAAO,OAAO,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,YAAiC;QACpD,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,IAAY;QACnC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,aAAa,CAAC;YACvB,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,qBAAqB,CAAC;YAC/B;gBACE,OAAO,sBAAsB,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,2BAA2B;QACtC,IAAI,CAAC;YACH,yDAAyD;YACzD,0BAA0B;YAC1B,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAEjD,qDAAqD;YACrD,MAAM,MAAM,GAAG,MAAM,iCAAY,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YACjF,eAAM,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,YAAY,wBAAwB,CAAC,CAAC;QAEzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAAC,MAAc;QAM9C,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpE,iCAAY,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;gBACvC,iCAAY,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBACnD,iCAAY,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;gBACtE,iCAAY,CAAC,SAAS,CAAC;oBACrB,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE;oBACtB,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;iBACjD,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAE;gBACtD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3B,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,WAAW;gBACX,MAAM,EAAE,SAAS;aAClB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO;gBACL,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,CAAC;gBACd,MAAM,EAAE,EAAE;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjfD,kDAifC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\notificationService.ts"],
      sourcesContent: ["import { User } from '../models/User.model';\r\nimport { PresenceService } from './presenceService';\r\nimport { logger } from '../utils/logger';\r\nimport {\r\n  Notification\r\n} from '../models/notification.model';\r\nimport emailService from './emailService';\r\n\r\nexport interface NotificationPayload {\r\n  id: string;\r\n  type: 'message' | 'match' | 'system' | 'property' | 'reminder';\r\n  title: string;\r\n  body: string;\r\n  data?: {\r\n    conversationId?: string;\r\n    messageId?: string;\r\n    senderId?: string;\r\n    senderName?: string;\r\n    senderAvatar?: string;\r\n    matchId?: string;\r\n    propertyId?: string;\r\n    [key: string]: any;\r\n  };\r\n  priority: 'high' | 'normal' | 'low';\r\n  createdAt: Date;\r\n  expiresAt?: Date;\r\n}\r\n\r\nexport interface PushNotificationConfig {\r\n  title: string;\r\n  body: string;\r\n  icon?: string;\r\n  badge?: string;\r\n  sound?: string;\r\n  clickAction?: string;\r\n  data?: any;\r\n}\r\n\r\nexport class NotificationService {\r\n  private static instance: NotificationService;\r\n  private presenceService: PresenceService;\r\n\r\n  constructor() {\r\n    this.presenceService = PresenceService.getInstance();\r\n  }\r\n\r\n  public static getInstance(): NotificationService {\r\n    if (!NotificationService.instance) {\r\n      NotificationService.instance = new NotificationService();\r\n    }\r\n    return NotificationService.instance;\r\n  }\r\n\r\n  /**\r\n   * Send message notification\r\n   */\r\n  public async sendMessageNotification(\r\n    message: any,\r\n    conversation: any,\r\n    recipientId: string\r\n  ): Promise<void> {\r\n    try {\r\n      // Don't send notification if recipient is online and active in the conversation\r\n      if (this.presenceService.isUserOnline(recipientId)) {\r\n        const userActivity = this.presenceService.getUserPresence(recipientId)?.currentActivity;\r\n        if (userActivity?.type === 'messaging' && userActivity.details === conversation._id.toString()) {\r\n          logger.debug(`Skipping notification for ${recipientId} - active in conversation`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Get recipient and sender info\r\n      const [recipient, sender] = await Promise.all([\r\n        User.findById(recipientId).select('firstName lastName email notificationSettings pushTokens'),\r\n        User.findById(message.senderId).select('firstName lastName avatar')\r\n      ]);\r\n\r\n      if (!recipient || !sender) {\r\n        logger.warn(`Missing user data for notification - recipient: ${!!recipient}, sender: ${!!sender}`);\r\n        return;\r\n      }\r\n\r\n      // Check if recipient has notifications enabled\r\n      if (!this.shouldSendNotification(recipient, 'message')) {\r\n        logger.debug(`Notifications disabled for user ${recipientId}`);\r\n        return;\r\n      }\r\n\r\n      // Check if conversation is muted\r\n      const participantDetail = conversation.participantDetails.find(\r\n        (pd: any) => pd.userId.toString() === recipientId\r\n      );\r\n\r\n      if (participantDetail?.isMuted) {\r\n        const now = new Date();\r\n        if (!participantDetail.mutedUntil || participantDetail.mutedUntil > now) {\r\n          logger.debug(`Conversation muted for user ${recipientId}`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Create notification payload\r\n      const notification: NotificationPayload = {\r\n        id: `msg_${message._id}_${Date.now()}`,\r\n        type: 'message',\r\n        title: this.getMessageNotificationTitle(sender, conversation),\r\n        body: this.getMessageNotificationBody(message),\r\n        data: {\r\n          conversationId: conversation._id.toString(),\r\n          messageId: message._id.toString(),\r\n          senderId: (sender as any)._id.toString(),\r\n          senderName: `${(sender as any).firstName} ${(sender as any).lastName}`,\r\n          senderAvatar: (sender as any).avatar,\r\n          messageType: message.messageType\r\n        },\r\n        priority: 'high',\r\n        createdAt: new Date(),\r\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours\r\n      };\r\n\r\n      // Send push notification\r\n      await this.sendPushNotification(recipient, notification);\r\n\r\n      // Send email notification if user is offline for more than 30 minutes\r\n      const userPresence = this.presenceService.getUserPresence(recipientId);\r\n      const isOfflineLong = !userPresence || \r\n        (userPresence.status === 'offline' && \r\n         Date.now() - userPresence.lastSeen.getTime() > 30 * 60 * 1000);\r\n\r\n      if (isOfflineLong && this.shouldSendEmailNotification(recipient, 'message')) {\r\n        await this.sendEmailNotification(recipient, notification, conversation);\r\n      }\r\n\r\n      logger.info(`Message notification sent to user ${recipientId} for message ${message._id}`);\r\n\r\n    } catch (error) {\r\n      logger.error('Error sending message notification:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send match notification\r\n   */\r\n  public async sendMatchNotification(\r\n    userId: string,\r\n    matchData: any\r\n  ): Promise<void> {\r\n    try {\r\n      const user = await User.findById(userId).select('firstName lastName email notificationSettings pushTokens');\r\n      \r\n      if (!user || !this.shouldSendNotification(user, 'match')) {\r\n        return;\r\n      }\r\n\r\n      const notification: NotificationPayload = {\r\n        id: `match_${matchData.id}_${Date.now()}`,\r\n        type: 'match',\r\n        title: '\uD83C\uDF89 New Match!',\r\n        body: `You have a new ${matchData.type} match with ${matchData.compatibilityScore}% compatibility!`,\r\n        data: {\r\n          matchId: matchData.id,\r\n          targetType: matchData.type,\r\n          compatibilityScore: matchData.compatibilityScore\r\n        },\r\n        priority: 'high',\r\n        createdAt: new Date()\r\n      };\r\n\r\n      await this.sendPushNotification(user, notification);\r\n\r\n      logger.info(`Match notification sent to user ${userId}`);\r\n\r\n    } catch (error) {\r\n      logger.error('Error sending match notification:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send system notification\r\n   */\r\n  public async sendSystemNotification(\r\n    userId: string,\r\n    title: string,\r\n    body: string,\r\n    data?: any\r\n  ): Promise<void> {\r\n    try {\r\n      const user = await User.findById(userId).select('firstName lastName email notificationSettings pushTokens');\r\n      \r\n      if (!user || !this.shouldSendNotification(user, 'system')) {\r\n        return;\r\n      }\r\n\r\n      const notification: NotificationPayload = {\r\n        id: `system_${userId}_${Date.now()}`,\r\n        type: 'system',\r\n        title,\r\n        body,\r\n        data,\r\n        priority: 'normal',\r\n        createdAt: new Date()\r\n      };\r\n\r\n      await this.sendPushNotification(user, notification);\r\n\r\n      logger.info(`System notification sent to user ${userId}: ${title}`);\r\n\r\n    } catch (error) {\r\n      logger.error('Error sending system notification:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send bulk notifications\r\n   */\r\n  public async sendBulkNotifications(\r\n    userIds: string[],\r\n    notification: Omit<NotificationPayload, 'id' | 'createdAt'>\r\n  ): Promise<void> {\r\n    try {\r\n      const users = await User.find({\r\n        _id: { $in: userIds }\r\n      }).select('firstName lastName email notificationSettings pushTokens');\r\n\r\n      const promises = users.map(user => {\r\n        if (this.shouldSendNotification(user, notification.type)) {\r\n          const fullNotification: NotificationPayload = {\r\n            ...notification,\r\n            id: `bulk_${user._id}_${Date.now()}`,\r\n            createdAt: new Date()\r\n          };\r\n          return this.sendPushNotification(user, fullNotification);\r\n        }\r\n        return Promise.resolve();\r\n      });\r\n\r\n      await Promise.allSettled(promises);\r\n\r\n      logger.info(`Bulk notification sent to ${users.length} users`);\r\n\r\n    } catch (error) {\r\n      logger.error('Error sending bulk notifications:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send push notification\r\n   */\r\n  private async sendPushNotification(\r\n    user: any,\r\n    notification: NotificationPayload\r\n  ): Promise<void> {\r\n    try {\r\n      // Check if user has push tokens\r\n      if (!user.pushTokens || user.pushTokens.length === 0) {\r\n        logger.debug(`No push tokens for user ${user._id}`);\r\n        return;\r\n      }\r\n\r\n      const pushConfig: PushNotificationConfig = {\r\n        title: notification.title,\r\n        body: notification.body,\r\n        icon: '/icons/icon-192x192.png',\r\n        badge: '/icons/badge-72x72.png',\r\n        sound: 'default',\r\n        clickAction: this.getClickAction(notification),\r\n        data: notification.data\r\n      };\r\n\r\n      // Here you would integrate with your push notification service\r\n      // Examples: Firebase Cloud Messaging (FCM), Apple Push Notification Service (APNs)\r\n      \r\n      // For now, we'll log the notification\r\n      logger.info(`Push notification would be sent to ${user.pushTokens.length} devices:`, {\r\n        userId: user._id,\r\n        title: pushConfig.title,\r\n        body: pushConfig.body,\r\n        type: notification.type\r\n      });\r\n\r\n      // TODO: Implement actual push notification sending\r\n      // await this.fcmService.sendToTokens(user.pushTokens, pushConfig);\r\n\r\n    } catch (error) {\r\n      logger.error('Error sending push notification:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send email notification\r\n   */\r\n  private async sendEmailNotification(\r\n    user: any,\r\n    notification: NotificationPayload,\r\n    conversation?: any\r\n  ): Promise<void> {\r\n    try {\r\n      // Check if user has email notifications enabled\r\n      if (!this.shouldSendEmailNotification(user, notification.type)) {\r\n        return;\r\n      }\r\n\r\n      const emailData = {\r\n        to: user.email,\r\n        subject: notification.title,\r\n        template: this.getEmailTemplate(notification.type),\r\n        data: {\r\n          userName: `${user.firstName} ${user.lastName}`,\r\n          notificationTitle: notification.title,\r\n          notificationBody: notification.body,\r\n          conversationTitle: conversation?.title,\r\n          senderName: notification.data?.senderName,\r\n          actionUrl: this.getActionUrl(notification),\r\n          unsubscribeUrl: `${process.env.FRONTEND_URL}/settings/notifications?token=${user._id}`\r\n        }\r\n      };\r\n\r\n      // Send email using the email service\r\n      const result = await emailService.sendEmail({\r\n        to: user.email,\r\n        subject: emailData.subject,\r\n        html: `\r\n          <h2>${emailData.data.notificationTitle}</h2>\r\n          <p>Hello ${emailData.data.userName}!</p>\r\n          <p>${emailData.data.notificationBody}</p>\r\n          ${emailData.data.actionUrl ? `<p><a href=\"${emailData.data.actionUrl}\">View Details</a></p>` : ''}\r\n          <p><a href=\"${emailData.data.unsubscribeUrl}\">Unsubscribe</a></p>\r\n        `,\r\n        text: `\r\n          ${emailData.data.notificationTitle}\r\n\r\n          Hello ${emailData.data.userName}!\r\n\r\n          ${emailData.data.notificationBody}\r\n\r\n          ${emailData.data.actionUrl ? `View details: ${emailData.data.actionUrl}` : ''}\r\n\r\n          Unsubscribe: ${emailData.data.unsubscribeUrl}\r\n        `\r\n      });\r\n\r\n      if (result.success) {\r\n        logger.info(`Email notification sent to ${user.email}:`, {\r\n          subject: emailData.subject,\r\n          messageId: result.messageId\r\n        });\r\n      } else {\r\n        logger.error(`Failed to send email notification to ${user.email}:`, result.error);\r\n      }\r\n\r\n    } catch (error) {\r\n      logger.error('Error sending email notification:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if notification should be sent\r\n   */\r\n  private shouldSendNotification(user: any, type: string): boolean {\r\n    if (!user.notificationSettings) {\r\n      return true; // Default to enabled\r\n    }\r\n\r\n    const settings = user.notificationSettings;\r\n    \r\n    switch (type) {\r\n      case 'message':\r\n        return settings.messages !== false;\r\n      case 'match':\r\n        return settings.matches !== false;\r\n      case 'system':\r\n        return settings.system !== false;\r\n      default:\r\n        return true;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if email notification should be sent\r\n   */\r\n  private shouldSendEmailNotification(user: any, type: string): boolean {\r\n    if (!user.notificationSettings?.email) {\r\n      return false;\r\n    }\r\n\r\n    const emailSettings = user.notificationSettings.email;\r\n    \r\n    switch (type) {\r\n      case 'message':\r\n        return emailSettings.messages !== false;\r\n      case 'match':\r\n        return emailSettings.matches !== false;\r\n      case 'system':\r\n        return emailSettings.system !== false;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get message notification title\r\n   */\r\n  private getMessageNotificationTitle(sender: any, conversation: any): string {\r\n    if (conversation.conversationType === 'direct') {\r\n      return `${sender.firstName} ${sender.lastName}`;\r\n    } else {\r\n      return conversation.title || 'Group Message';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get message notification body\r\n   */\r\n  private getMessageNotificationBody(message: any): string {\r\n    switch (message.messageType) {\r\n      case 'text':\r\n        return message.content.length > 100 \r\n          ? `${message.content.substring(0, 100)}...`\r\n          : message.content;\r\n      case 'image':\r\n        return '\uD83D\uDCF7 Sent a photo';\r\n      case 'file':\r\n        return '\uD83D\uDCCE Sent a file';\r\n      case 'location':\r\n        return '\uD83D\uDCCD Shared location';\r\n      case 'property_share':\r\n        return '\uD83C\uDFE0 Shared a property';\r\n      case 'system':\r\n        return message.content;\r\n      default:\r\n        return 'Sent a message';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get click action URL\r\n   */\r\n  private getClickAction(notification: NotificationPayload): string {\r\n    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';\r\n    \r\n    switch (notification.type) {\r\n      case 'message':\r\n        return `${baseUrl}/messages/${notification.data?.conversationId}`;\r\n      case 'match':\r\n        return `${baseUrl}/matches`;\r\n      case 'system':\r\n        return `${baseUrl}/notifications`;\r\n      default:\r\n        return baseUrl;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get action URL for emails\r\n   */\r\n  private getActionUrl(notification: NotificationPayload): string {\r\n    return this.getClickAction(notification);\r\n  }\r\n\r\n  /**\r\n   * Get email template name\r\n   */\r\n  private getEmailTemplate(type: string): string {\r\n    switch (type) {\r\n      case 'message':\r\n        return 'new-message';\r\n      case 'match':\r\n        return 'new-match';\r\n      case 'system':\r\n        return 'system-notification';\r\n      default:\r\n        return 'general-notification';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule notification cleanup\r\n   */\r\n  public async cleanupExpiredNotifications(): Promise<void> {\r\n    try {\r\n      // This would clean up stored notifications from database\r\n      // For now, we'll just log\r\n      logger.info('Cleaning up expired notifications');\r\n      \r\n      // Clean up expired notifications using the new model\r\n      const result = await Notification.deleteMany({ expiresAt: { $lt: new Date() } });\r\n      logger.info(`Cleaned up ${result.deletedCount} expired notifications`);\r\n      \r\n    } catch (error) {\r\n      logger.error('Error cleaning up notifications:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get notification statistics\r\n   */\r\n  public async getNotificationStats(userId: string): Promise<{\r\n    totalSent: number;\r\n    totalRead: number;\r\n    totalUnread: number;\r\n    byType: { [key: string]: number };\r\n  }> {\r\n    try {\r\n      // Get notification statistics from the new model\r\n      const [totalSent, totalRead, totalUnread, byType] = await Promise.all([\r\n        Notification.countDocuments({ userId }),\r\n        Notification.countDocuments({ userId, read: true }),\r\n        Notification.countDocuments({ userId, read: false, dismissed: false }),\r\n        Notification.aggregate([\r\n          { $match: { userId } },\r\n          { $group: { _id: '$type', count: { $sum: 1 } } }\r\n        ])\r\n      ]);\r\n\r\n      const typeStats = byType.reduce((acc: any, item: any) => {\r\n        acc[item._id] = item.count;\r\n        return acc;\r\n      }, {});\r\n\r\n      return {\r\n        totalSent,\r\n        totalRead,\r\n        totalUnread,\r\n        byType: typeStats\r\n      };\r\n    } catch (error) {\r\n      logger.error('Error getting notification stats:', error);\r\n      return {\r\n        totalSent: 0,\r\n        totalRead: 0,\r\n        totalUnread: 0,\r\n        byType: {}\r\n      };\r\n    }\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "44b6ee0dd5c68abfa2188b3d92731cf5a8837d1b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_20oq5kwdlb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_20oq5kwdlb();
var __importDefault =
/* istanbul ignore next */
(cov_20oq5kwdlb().s[0]++,
/* istanbul ignore next */
(cov_20oq5kwdlb().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_20oq5kwdlb().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_20oq5kwdlb().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_20oq5kwdlb().f[0]++;
  cov_20oq5kwdlb().s[1]++;
  return /* istanbul ignore next */(cov_20oq5kwdlb().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_20oq5kwdlb().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_20oq5kwdlb().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_20oq5kwdlb().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_20oq5kwdlb().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_20oq5kwdlb().s[3]++;
exports.NotificationService = void 0;
const User_model_1 =
/* istanbul ignore next */
(cov_20oq5kwdlb().s[4]++, require("../models/User.model"));
const presenceService_1 =
/* istanbul ignore next */
(cov_20oq5kwdlb().s[5]++, require("./presenceService"));
const logger_1 =
/* istanbul ignore next */
(cov_20oq5kwdlb().s[6]++, require("../utils/logger"));
const notification_model_1 =
/* istanbul ignore next */
(cov_20oq5kwdlb().s[7]++, require("../models/notification.model"));
const emailService_1 =
/* istanbul ignore next */
(cov_20oq5kwdlb().s[8]++, __importDefault(require("./emailService")));
class NotificationService {
  constructor() {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[1]++;
    cov_20oq5kwdlb().s[9]++;
    this.presenceService = presenceService_1.PresenceService.getInstance();
  }
  static getInstance() {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[2]++;
    cov_20oq5kwdlb().s[10]++;
    if (!NotificationService.instance) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().b[3][0]++;
      cov_20oq5kwdlb().s[11]++;
      NotificationService.instance = new NotificationService();
    } else
    /* istanbul ignore next */
    {
      cov_20oq5kwdlb().b[3][1]++;
    }
    cov_20oq5kwdlb().s[12]++;
    return NotificationService.instance;
  }
  /**
   * Send message notification
   */
  async sendMessageNotification(message, conversation, recipientId) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[3]++;
    cov_20oq5kwdlb().s[13]++;
    try {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[14]++;
      // Don't send notification if recipient is online and active in the conversation
      if (this.presenceService.isUserOnline(recipientId)) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[4][0]++;
        const userActivity =
        /* istanbul ignore next */
        (cov_20oq5kwdlb().s[15]++, this.presenceService.getUserPresence(recipientId)?.currentActivity);
        /* istanbul ignore next */
        cov_20oq5kwdlb().s[16]++;
        if (
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[6][0]++, userActivity?.type === 'messaging') &&
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[6][1]++, userActivity.details === conversation._id.toString())) {
          /* istanbul ignore next */
          cov_20oq5kwdlb().b[5][0]++;
          cov_20oq5kwdlb().s[17]++;
          logger_1.logger.debug(`Skipping notification for ${recipientId} - active in conversation`);
          /* istanbul ignore next */
          cov_20oq5kwdlb().s[18]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_20oq5kwdlb().b[5][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[4][1]++;
      }
      // Get recipient and sender info
      const [recipient, sender] =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[19]++, await Promise.all([User_model_1.User.findById(recipientId).select('firstName lastName email notificationSettings pushTokens'), User_model_1.User.findById(message.senderId).select('firstName lastName avatar')]));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[20]++;
      if (
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[8][0]++, !recipient) ||
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[8][1]++, !sender)) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[7][0]++;
        cov_20oq5kwdlb().s[21]++;
        logger_1.logger.warn(`Missing user data for notification - recipient: ${!!recipient}, sender: ${!!sender}`);
        /* istanbul ignore next */
        cov_20oq5kwdlb().s[22]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[7][1]++;
      }
      // Check if recipient has notifications enabled
      cov_20oq5kwdlb().s[23]++;
      if (!this.shouldSendNotification(recipient, 'message')) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[9][0]++;
        cov_20oq5kwdlb().s[24]++;
        logger_1.logger.debug(`Notifications disabled for user ${recipientId}`);
        /* istanbul ignore next */
        cov_20oq5kwdlb().s[25]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[9][1]++;
      }
      // Check if conversation is muted
      const participantDetail =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[26]++, conversation.participantDetails.find(pd => {
        /* istanbul ignore next */
        cov_20oq5kwdlb().f[4]++;
        cov_20oq5kwdlb().s[27]++;
        return pd.userId.toString() === recipientId;
      }));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[28]++;
      if (participantDetail?.isMuted) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[10][0]++;
        const now =
        /* istanbul ignore next */
        (cov_20oq5kwdlb().s[29]++, new Date());
        /* istanbul ignore next */
        cov_20oq5kwdlb().s[30]++;
        if (
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[12][0]++, !participantDetail.mutedUntil) ||
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[12][1]++, participantDetail.mutedUntil > now)) {
          /* istanbul ignore next */
          cov_20oq5kwdlb().b[11][0]++;
          cov_20oq5kwdlb().s[31]++;
          logger_1.logger.debug(`Conversation muted for user ${recipientId}`);
          /* istanbul ignore next */
          cov_20oq5kwdlb().s[32]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_20oq5kwdlb().b[11][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[10][1]++;
      }
      // Create notification payload
      const notification =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[33]++, {
        id: `msg_${message._id}_${Date.now()}`,
        type: 'message',
        title: this.getMessageNotificationTitle(sender, conversation),
        body: this.getMessageNotificationBody(message),
        data: {
          conversationId: conversation._id.toString(),
          messageId: message._id.toString(),
          senderId: sender._id.toString(),
          senderName: `${sender.firstName} ${sender.lastName}`,
          senderAvatar: sender.avatar,
          messageType: message.messageType
        },
        priority: 'high',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      });
      // Send push notification
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[34]++;
      await this.sendPushNotification(recipient, notification);
      // Send email notification if user is offline for more than 30 minutes
      const userPresence =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[35]++, this.presenceService.getUserPresence(recipientId));
      const isOfflineLong =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[36]++,
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[13][0]++, !userPresence) ||
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[13][1]++, userPresence.status === 'offline') &&
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[13][2]++, Date.now() - userPresence.lastSeen.getTime() > 30 * 60 * 1000));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[37]++;
      if (
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[15][0]++, isOfflineLong) &&
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[15][1]++, this.shouldSendEmailNotification(recipient, 'message'))) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[14][0]++;
        cov_20oq5kwdlb().s[38]++;
        await this.sendEmailNotification(recipient, notification, conversation);
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[14][1]++;
      }
      cov_20oq5kwdlb().s[39]++;
      logger_1.logger.info(`Message notification sent to user ${recipientId} for message ${message._id}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[40]++;
      logger_1.logger.error('Error sending message notification:', error);
    }
  }
  /**
   * Send match notification
   */
  async sendMatchNotification(userId, matchData) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[5]++;
    cov_20oq5kwdlb().s[41]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[42]++, await User_model_1.User.findById(userId).select('firstName lastName email notificationSettings pushTokens'));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[43]++;
      if (
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[17][0]++, !user) ||
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[17][1]++, !this.shouldSendNotification(user, 'match'))) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[16][0]++;
        cov_20oq5kwdlb().s[44]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[16][1]++;
      }
      const notification =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[45]++, {
        id: `match_${matchData.id}_${Date.now()}`,
        type: 'match',
        title: '🎉 New Match!',
        body: `You have a new ${matchData.type} match with ${matchData.compatibilityScore}% compatibility!`,
        data: {
          matchId: matchData.id,
          targetType: matchData.type,
          compatibilityScore: matchData.compatibilityScore
        },
        priority: 'high',
        createdAt: new Date()
      });
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[46]++;
      await this.sendPushNotification(user, notification);
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[47]++;
      logger_1.logger.info(`Match notification sent to user ${userId}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[48]++;
      logger_1.logger.error('Error sending match notification:', error);
    }
  }
  /**
   * Send system notification
   */
  async sendSystemNotification(userId, title, body, data) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[6]++;
    cov_20oq5kwdlb().s[49]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[50]++, await User_model_1.User.findById(userId).select('firstName lastName email notificationSettings pushTokens'));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[51]++;
      if (
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[19][0]++, !user) ||
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[19][1]++, !this.shouldSendNotification(user, 'system'))) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[18][0]++;
        cov_20oq5kwdlb().s[52]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[18][1]++;
      }
      const notification =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[53]++, {
        id: `system_${userId}_${Date.now()}`,
        type: 'system',
        title,
        body,
        data,
        priority: 'normal',
        createdAt: new Date()
      });
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[54]++;
      await this.sendPushNotification(user, notification);
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[55]++;
      logger_1.logger.info(`System notification sent to user ${userId}: ${title}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[56]++;
      logger_1.logger.error('Error sending system notification:', error);
    }
  }
  /**
   * Send bulk notifications
   */
  async sendBulkNotifications(userIds, notification) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[7]++;
    cov_20oq5kwdlb().s[57]++;
    try {
      const users =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[58]++, await User_model_1.User.find({
        _id: {
          $in: userIds
        }
      }).select('firstName lastName email notificationSettings pushTokens'));
      const promises =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[59]++, users.map(user => {
        /* istanbul ignore next */
        cov_20oq5kwdlb().f[8]++;
        cov_20oq5kwdlb().s[60]++;
        if (this.shouldSendNotification(user, notification.type)) {
          /* istanbul ignore next */
          cov_20oq5kwdlb().b[20][0]++;
          const fullNotification =
          /* istanbul ignore next */
          (cov_20oq5kwdlb().s[61]++, {
            ...notification,
            id: `bulk_${user._id}_${Date.now()}`,
            createdAt: new Date()
          });
          /* istanbul ignore next */
          cov_20oq5kwdlb().s[62]++;
          return this.sendPushNotification(user, fullNotification);
        } else
        /* istanbul ignore next */
        {
          cov_20oq5kwdlb().b[20][1]++;
        }
        cov_20oq5kwdlb().s[63]++;
        return Promise.resolve();
      }));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[64]++;
      await Promise.allSettled(promises);
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[65]++;
      logger_1.logger.info(`Bulk notification sent to ${users.length} users`);
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[66]++;
      logger_1.logger.error('Error sending bulk notifications:', error);
    }
  }
  /**
   * Send push notification
   */
  async sendPushNotification(user, notification) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[9]++;
    cov_20oq5kwdlb().s[67]++;
    try {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[68]++;
      // Check if user has push tokens
      if (
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[22][0]++, !user.pushTokens) ||
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[22][1]++, user.pushTokens.length === 0)) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[21][0]++;
        cov_20oq5kwdlb().s[69]++;
        logger_1.logger.debug(`No push tokens for user ${user._id}`);
        /* istanbul ignore next */
        cov_20oq5kwdlb().s[70]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[21][1]++;
      }
      const pushConfig =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[71]++, {
        title: notification.title,
        body: notification.body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        sound: 'default',
        clickAction: this.getClickAction(notification),
        data: notification.data
      });
      // Here you would integrate with your push notification service
      // Examples: Firebase Cloud Messaging (FCM), Apple Push Notification Service (APNs)
      // For now, we'll log the notification
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[72]++;
      logger_1.logger.info(`Push notification would be sent to ${user.pushTokens.length} devices:`, {
        userId: user._id,
        title: pushConfig.title,
        body: pushConfig.body,
        type: notification.type
      });
      // TODO: Implement actual push notification sending
      // await this.fcmService.sendToTokens(user.pushTokens, pushConfig);
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[73]++;
      logger_1.logger.error('Error sending push notification:', error);
    }
  }
  /**
   * Send email notification
   */
  async sendEmailNotification(user, notification, conversation) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[10]++;
    cov_20oq5kwdlb().s[74]++;
    try {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[75]++;
      // Check if user has email notifications enabled
      if (!this.shouldSendEmailNotification(user, notification.type)) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[23][0]++;
        cov_20oq5kwdlb().s[76]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_20oq5kwdlb().b[23][1]++;
      }
      const emailData =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[77]++, {
        to: user.email,
        subject: notification.title,
        template: this.getEmailTemplate(notification.type),
        data: {
          userName: `${user.firstName} ${user.lastName}`,
          notificationTitle: notification.title,
          notificationBody: notification.body,
          conversationTitle: conversation?.title,
          senderName: notification.data?.senderName,
          actionUrl: this.getActionUrl(notification),
          unsubscribeUrl: `${process.env.FRONTEND_URL}/settings/notifications?token=${user._id}`
        }
      });
      // Send email using the email service
      const result =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[78]++, await emailService_1.default.sendEmail({
        to: user.email,
        subject: emailData.subject,
        html: `
          <h2>${emailData.data.notificationTitle}</h2>
          <p>Hello ${emailData.data.userName}!</p>
          <p>${emailData.data.notificationBody}</p>
          ${emailData.data.actionUrl ?
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[24][0]++, `<p><a href="${emailData.data.actionUrl}">View Details</a></p>`) :
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[24][1]++, '')}
          <p><a href="${emailData.data.unsubscribeUrl}">Unsubscribe</a></p>
        `,
        text: `
          ${emailData.data.notificationTitle}

          Hello ${emailData.data.userName}!

          ${emailData.data.notificationBody}

          ${emailData.data.actionUrl ?
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[25][0]++, `View details: ${emailData.data.actionUrl}`) :
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[25][1]++, '')}

          Unsubscribe: ${emailData.data.unsubscribeUrl}
        `
      }));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[79]++;
      if (result.success) {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[26][0]++;
        cov_20oq5kwdlb().s[80]++;
        logger_1.logger.info(`Email notification sent to ${user.email}:`, {
          subject: emailData.subject,
          messageId: result.messageId
        });
      } else {
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[26][1]++;
        cov_20oq5kwdlb().s[81]++;
        logger_1.logger.error(`Failed to send email notification to ${user.email}:`, result.error);
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[82]++;
      logger_1.logger.error('Error sending email notification:', error);
    }
  }
  /**
   * Check if notification should be sent
   */
  shouldSendNotification(user, type) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[11]++;
    cov_20oq5kwdlb().s[83]++;
    if (!user.notificationSettings) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().b[27][0]++;
      cov_20oq5kwdlb().s[84]++;
      return true; // Default to enabled
    } else
    /* istanbul ignore next */
    {
      cov_20oq5kwdlb().b[27][1]++;
    }
    const settings =
    /* istanbul ignore next */
    (cov_20oq5kwdlb().s[85]++, user.notificationSettings);
    /* istanbul ignore next */
    cov_20oq5kwdlb().s[86]++;
    switch (type) {
      case 'message':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[28][0]++;
        cov_20oq5kwdlb().s[87]++;
        return settings.messages !== false;
      case 'match':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[28][1]++;
        cov_20oq5kwdlb().s[88]++;
        return settings.matches !== false;
      case 'system':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[28][2]++;
        cov_20oq5kwdlb().s[89]++;
        return settings.system !== false;
      default:
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[28][3]++;
        cov_20oq5kwdlb().s[90]++;
        return true;
    }
  }
  /**
   * Check if email notification should be sent
   */
  shouldSendEmailNotification(user, type) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[12]++;
    cov_20oq5kwdlb().s[91]++;
    if (!user.notificationSettings?.email) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().b[29][0]++;
      cov_20oq5kwdlb().s[92]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_20oq5kwdlb().b[29][1]++;
    }
    const emailSettings =
    /* istanbul ignore next */
    (cov_20oq5kwdlb().s[93]++, user.notificationSettings.email);
    /* istanbul ignore next */
    cov_20oq5kwdlb().s[94]++;
    switch (type) {
      case 'message':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[30][0]++;
        cov_20oq5kwdlb().s[95]++;
        return emailSettings.messages !== false;
      case 'match':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[30][1]++;
        cov_20oq5kwdlb().s[96]++;
        return emailSettings.matches !== false;
      case 'system':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[30][2]++;
        cov_20oq5kwdlb().s[97]++;
        return emailSettings.system !== false;
      default:
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[30][3]++;
        cov_20oq5kwdlb().s[98]++;
        return false;
    }
  }
  /**
   * Get message notification title
   */
  getMessageNotificationTitle(sender, conversation) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[13]++;
    cov_20oq5kwdlb().s[99]++;
    if (conversation.conversationType === 'direct') {
      /* istanbul ignore next */
      cov_20oq5kwdlb().b[31][0]++;
      cov_20oq5kwdlb().s[100]++;
      return `${sender.firstName} ${sender.lastName}`;
    } else {
      /* istanbul ignore next */
      cov_20oq5kwdlb().b[31][1]++;
      cov_20oq5kwdlb().s[101]++;
      return /* istanbul ignore next */(cov_20oq5kwdlb().b[32][0]++, conversation.title) ||
      /* istanbul ignore next */
      (cov_20oq5kwdlb().b[32][1]++, 'Group Message');
    }
  }
  /**
   * Get message notification body
   */
  getMessageNotificationBody(message) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[14]++;
    cov_20oq5kwdlb().s[102]++;
    switch (message.messageType) {
      case 'text':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][0]++;
        cov_20oq5kwdlb().s[103]++;
        return message.content.length > 100 ?
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[34][0]++, `${message.content.substring(0, 100)}...`) :
        /* istanbul ignore next */
        (cov_20oq5kwdlb().b[34][1]++, message.content);
      case 'image':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][1]++;
        cov_20oq5kwdlb().s[104]++;
        return '📷 Sent a photo';
      case 'file':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][2]++;
        cov_20oq5kwdlb().s[105]++;
        return '📎 Sent a file';
      case 'location':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][3]++;
        cov_20oq5kwdlb().s[106]++;
        return '📍 Shared location';
      case 'property_share':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][4]++;
        cov_20oq5kwdlb().s[107]++;
        return '🏠 Shared a property';
      case 'system':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][5]++;
        cov_20oq5kwdlb().s[108]++;
        return message.content;
      default:
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[33][6]++;
        cov_20oq5kwdlb().s[109]++;
        return 'Sent a message';
    }
  }
  /**
   * Get click action URL
   */
  getClickAction(notification) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[15]++;
    const baseUrl =
    /* istanbul ignore next */
    (cov_20oq5kwdlb().s[110]++,
    /* istanbul ignore next */
    (cov_20oq5kwdlb().b[35][0]++, process.env.FRONTEND_URL) ||
    /* istanbul ignore next */
    (cov_20oq5kwdlb().b[35][1]++, 'http://localhost:3000'));
    /* istanbul ignore next */
    cov_20oq5kwdlb().s[111]++;
    switch (notification.type) {
      case 'message':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[36][0]++;
        cov_20oq5kwdlb().s[112]++;
        return `${baseUrl}/messages/${notification.data?.conversationId}`;
      case 'match':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[36][1]++;
        cov_20oq5kwdlb().s[113]++;
        return `${baseUrl}/matches`;
      case 'system':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[36][2]++;
        cov_20oq5kwdlb().s[114]++;
        return `${baseUrl}/notifications`;
      default:
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[36][3]++;
        cov_20oq5kwdlb().s[115]++;
        return baseUrl;
    }
  }
  /**
   * Get action URL for emails
   */
  getActionUrl(notification) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[16]++;
    cov_20oq5kwdlb().s[116]++;
    return this.getClickAction(notification);
  }
  /**
   * Get email template name
   */
  getEmailTemplate(type) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[17]++;
    cov_20oq5kwdlb().s[117]++;
    switch (type) {
      case 'message':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[37][0]++;
        cov_20oq5kwdlb().s[118]++;
        return 'new-message';
      case 'match':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[37][1]++;
        cov_20oq5kwdlb().s[119]++;
        return 'new-match';
      case 'system':
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[37][2]++;
        cov_20oq5kwdlb().s[120]++;
        return 'system-notification';
      default:
        /* istanbul ignore next */
        cov_20oq5kwdlb().b[37][3]++;
        cov_20oq5kwdlb().s[121]++;
        return 'general-notification';
    }
  }
  /**
   * Schedule notification cleanup
   */
  async cleanupExpiredNotifications() {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[18]++;
    cov_20oq5kwdlb().s[122]++;
    try {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[123]++;
      // This would clean up stored notifications from database
      // For now, we'll just log
      logger_1.logger.info('Cleaning up expired notifications');
      // Clean up expired notifications using the new model
      const result =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[124]++, await notification_model_1.Notification.deleteMany({
        expiresAt: {
          $lt: new Date()
        }
      }));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[125]++;
      logger_1.logger.info(`Cleaned up ${result.deletedCount} expired notifications`);
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[126]++;
      logger_1.logger.error('Error cleaning up notifications:', error);
    }
  }
  /**
   * Get notification statistics
   */
  async getNotificationStats(userId) {
    /* istanbul ignore next */
    cov_20oq5kwdlb().f[19]++;
    cov_20oq5kwdlb().s[127]++;
    try {
      // Get notification statistics from the new model
      const [totalSent, totalRead, totalUnread, byType] =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[128]++, await Promise.all([notification_model_1.Notification.countDocuments({
        userId
      }), notification_model_1.Notification.countDocuments({
        userId,
        read: true
      }), notification_model_1.Notification.countDocuments({
        userId,
        read: false,
        dismissed: false
      }), notification_model_1.Notification.aggregate([{
        $match: {
          userId
        }
      }, {
        $group: {
          _id: '$type',
          count: {
            $sum: 1
          }
        }
      }])]));
      const typeStats =
      /* istanbul ignore next */
      (cov_20oq5kwdlb().s[129]++, byType.reduce((acc, item) => {
        /* istanbul ignore next */
        cov_20oq5kwdlb().f[20]++;
        cov_20oq5kwdlb().s[130]++;
        acc[item._id] = item.count;
        /* istanbul ignore next */
        cov_20oq5kwdlb().s[131]++;
        return acc;
      }, {}));
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[132]++;
      return {
        totalSent,
        totalRead,
        totalUnread,
        byType: typeStats
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[133]++;
      logger_1.logger.error('Error getting notification stats:', error);
      /* istanbul ignore next */
      cov_20oq5kwdlb().s[134]++;
      return {
        totalSent: 0,
        totalRead: 0,
        totalUnread: 0,
        byType: {}
      };
    }
  }
}
/* istanbul ignore next */
cov_20oq5kwdlb().s[135]++;
exports.NotificationService = NotificationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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