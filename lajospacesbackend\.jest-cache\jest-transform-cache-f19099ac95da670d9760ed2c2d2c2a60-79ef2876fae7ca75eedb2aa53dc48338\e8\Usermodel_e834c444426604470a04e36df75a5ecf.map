{"version": 3, "names": ["mongoose_1", "cov_1yeezh8cfg", "s", "__importStar", "require", "bcryptjs_1", "__importDefault", "UserSchema", "<PERSON><PERSON><PERSON>", "email", "type", "String", "required", "unique", "lowercase", "trim", "match", "index", "password", "minlength", "select", "firstName", "maxlength", "lastName", "dateOfBirth", "Date", "validate", "validator", "value", "f", "age", "getFullYear", "b", "message", "gender", "enum", "values", "phoneNumber", "sparse", "isEmailVerified", "Boolean", "default", "isPhoneVerified", "isActive", "accountType", "emailVerificationToken", "emailVerificationExpires", "passwordResetToken", "passwordResetExpires", "refreshTokens", "profileCompletionScore", "Number", "min", "max", "lastLoginAt", "lastActiveAt", "now", "preferences", "emailNotifications", "pushNotifications", "smsNotifications", "marketingEmails", "location", "city", "state", "country", "coordinates", "coords", "length", "timestamps", "toJSON", "virtuals", "transform", "_doc", "ret", "toObject", "createdAt", "virtual", "get", "pre", "next", "isModified", "salt", "genSalt", "hash", "error", "calculateProfileCompletion", "methods", "comparePassword", "candidate<PERSON><PERSON><PERSON>", "compare", "generateEmailVerificationToken", "crypto", "token", "randomBytes", "toString", "createHash", "update", "digest", "generatePasswordResetToken", "score", "Math", "getFullName", "getAge", "today", "birthDate", "monthDiff", "getMonth", "getDate", "exports", "User", "model"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\User.model.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\r\nimport bcrypt from 'bcryptjs';\r\n\r\n// User interface\r\nexport interface IUser extends Document {\r\n  // Basic Information\r\n  email: string;\r\n  password: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  dateOfBirth: Date;\r\n  gender: 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';\r\n  phoneNumber?: string;\r\n\r\n  // Account Status\r\n  isEmailVerified: boolean;\r\n  isPhoneVerified: boolean;\r\n  isActive: boolean;\r\n  accountType: 'seeker' | 'owner' | 'both';\r\n\r\n  // Authentication\r\n  emailVerificationToken?: string;\r\n  emailVerificationExpires?: Date;\r\n  passwordResetToken?: string;\r\n  passwordResetExpires?: Date;\r\n  refreshTokens: string[];\r\n\r\n  // Profile Completion\r\n  profileCompletionScore: number;\r\n  lastLoginAt?: Date;\r\n  lastActiveAt: Date;\r\n\r\n  // Preferences\r\n  preferences: {\r\n    emailNotifications: boolean;\r\n    pushNotifications: boolean;\r\n    smsNotifications: boolean;\r\n    marketingEmails: boolean;\r\n  };\r\n\r\n  // Location\r\n  location?: {\r\n    city: string;\r\n    state: string;\r\n    country: string;\r\n    coordinates?: {\r\n      type: 'Point';\r\n      coordinates: [number, number]; // [longitude, latitude]\r\n    };\r\n  };\r\n\r\n  // Timestamps\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n\r\n  // Methods\r\n  comparePassword(candidatePassword: string): Promise<boolean>;\r\n  generateEmailVerificationToken(): string;\r\n  generatePasswordResetToken(): string;\r\n  calculateProfileCompletion(): number;\r\n  getFullName(): string;\r\n  getAge(): number;\r\n}\r\n\r\n// User Schema\r\nconst UserSchema = new Schema<IUser>({\r\n  // Basic Information\r\n  email: {\r\n    type: String,\r\n    required: [true, 'Email is required'],\r\n    unique: true,\r\n    lowercase: true,\r\n    trim: true,\r\n    match: [\r\n      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\r\n      'Please provide a valid email address'\r\n    ],\r\n    index: true\r\n  },\r\n  \r\n  password: {\r\n    type: String,\r\n    required: [true, 'Password is required'],\r\n    minlength: [8, 'Password must be at least 8 characters long'],\r\n    select: false // Don't include password in queries by default\r\n  },\r\n  \r\n  firstName: {\r\n    type: String,\r\n    required: [true, 'First name is required'],\r\n    trim: true,\r\n    minlength: [2, 'First name must be at least 2 characters'],\r\n    maxlength: [50, 'First name cannot exceed 50 characters']\r\n  },\r\n  \r\n  lastName: {\r\n    type: String,\r\n    required: [true, 'Last name is required'],\r\n    trim: true,\r\n    minlength: [2, 'Last name must be at least 2 characters'],\r\n    maxlength: [50, 'Last name cannot exceed 50 characters']\r\n  },\r\n  \r\n  dateOfBirth: {\r\n    type: Date,\r\n    required: [true, 'Date of birth is required'],\r\n    validate: {\r\n      validator: function(value: Date) {\r\n        const age = new Date().getFullYear() - value.getFullYear();\r\n        return age >= 18 && age <= 100;\r\n      },\r\n      message: 'You must be between 18 and 100 years old'\r\n    }\r\n  },\r\n  \r\n  gender: {\r\n    type: String,\r\n    required: [true, 'Gender is required'],\r\n    enum: {\r\n      values: ['male', 'female', 'non-binary', 'prefer-not-to-say'],\r\n      message: 'Gender must be one of: male, female, non-binary, prefer-not-to-say'\r\n    }\r\n  },\r\n  \r\n  phoneNumber: {\r\n    type: String,\r\n    trim: true,\r\n    match: [/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Please provide a valid phone number'],\r\n    sparse: true // Allow multiple null values\r\n  },\r\n\r\n  // Account Status\r\n  isEmailVerified: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  \r\n  isPhoneVerified: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  \r\n  isActive: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  \r\n  accountType: {\r\n    type: String,\r\n    required: [true, 'Account type is required'],\r\n    enum: {\r\n      values: ['seeker', 'owner', 'both'],\r\n      message: 'Account type must be one of: seeker, owner, both'\r\n    },\r\n    default: 'seeker'\r\n  },\r\n\r\n  // Authentication\r\n  emailVerificationToken: {\r\n    type: String,\r\n    select: false\r\n  },\r\n  \r\n  emailVerificationExpires: {\r\n    type: Date,\r\n    select: false\r\n  },\r\n  \r\n  passwordResetToken: {\r\n    type: String,\r\n    select: false\r\n  },\r\n  \r\n  passwordResetExpires: {\r\n    type: Date,\r\n    select: false\r\n  },\r\n  \r\n  refreshTokens: [{\r\n    type: String,\r\n    select: false\r\n  }],\r\n\r\n  // Profile Completion\r\n  profileCompletionScore: {\r\n    type: Number,\r\n    default: 0,\r\n    min: 0,\r\n    max: 100\r\n  },\r\n  \r\n  lastLoginAt: {\r\n    type: Date\r\n  },\r\n  \r\n  lastActiveAt: {\r\n    type: Date,\r\n    default: Date.now\r\n  },\r\n\r\n  // Preferences\r\n  preferences: {\r\n    emailNotifications: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    pushNotifications: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    smsNotifications: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    marketingEmails: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n\r\n  // Location\r\n  location: {\r\n    city: {\r\n      type: String,\r\n      trim: true\r\n    },\r\n    state: {\r\n      type: String,\r\n      trim: true\r\n    },\r\n    country: {\r\n      type: String,\r\n      trim: true,\r\n      default: 'Nigeria'\r\n    },\r\n    coordinates: {\r\n      type: {\r\n        type: String,\r\n        enum: ['Point'],\r\n        required: function() {\r\n          return this.coordinates && this.coordinates.coordinates;\r\n        }\r\n      },\r\n      coordinates: {\r\n        type: [Number], // [longitude, latitude]\r\n        validate: {\r\n          validator: function(coords: number[]) {\r\n            return !coords || (coords.length === 2 &&\r\n                   coords[0] >= -180 && coords[0] <= 180 && // longitude\r\n                   coords[1] >= -90 && coords[1] <= 90);     // latitude\r\n          },\r\n          message: 'Coordinates must be [longitude, latitude] with valid ranges'\r\n        }\r\n      }\r\n    }\r\n  }\r\n}, {\r\n  timestamps: true,\r\n  toJSON: { \r\n    virtuals: true,\r\n    transform: function(_doc, ret) {\r\n      delete ret.password;\r\n      delete ret.refreshTokens;\r\n      delete ret.emailVerificationToken;\r\n      delete ret.passwordResetToken;\r\n      return ret;\r\n    }\r\n  },\r\n  toObject: { virtuals: true }\r\n});\r\n\r\n// Indexes for performance\r\nUserSchema.index({ email: 1 }, { unique: true });\r\nUserSchema.index({ 'location.coordinates': '2dsphere' }, { sparse: true });\r\nUserSchema.index({ accountType: 1 });\r\nUserSchema.index({ isActive: 1 });\r\nUserSchema.index({ createdAt: -1 });\r\nUserSchema.index({ lastActiveAt: -1 });\r\n\r\n// Virtual for full name\r\nUserSchema.virtual('fullName').get(function() {\r\n  return `${this.firstName} ${this.lastName}`;\r\n});\r\n\r\n// Virtual for age\r\nUserSchema.virtual('age').get(function() {\r\n  return new Date().getFullYear() - this.dateOfBirth.getFullYear();\r\n});\r\n\r\n// Pre-save middleware to hash password\r\nUserSchema.pre('save', async function(next) {\r\n  // Only hash password if it's modified\r\n  if (!this.isModified('password')) return next();\r\n  \r\n  try {\r\n    // Hash password with cost of 12\r\n    const salt = await bcrypt.genSalt(12);\r\n    this.password = await bcrypt.hash(this.password, salt);\r\n    next();\r\n  } catch (error) {\r\n    next(error as Error);\r\n  }\r\n});\r\n\r\n// Pre-save middleware to calculate profile completion\r\nUserSchema.pre('save', function(next) {\r\n  this.profileCompletionScore = this.calculateProfileCompletion();\r\n  next();\r\n});\r\n\r\n// Instance method to compare password\r\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\r\n  return bcrypt.compare(candidatePassword, this.password);\r\n};\r\n\r\n// Instance method to generate email verification token\r\nUserSchema.methods.generateEmailVerificationToken = function(): string {\r\n  const crypto = require('crypto');\r\n  const token = crypto.randomBytes(32).toString('hex');\r\n  \r\n  this.emailVerificationToken = crypto.createHash('sha256').update(token).digest('hex');\r\n  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\r\n  \r\n  return token;\r\n};\r\n\r\n// Instance method to generate password reset token\r\nUserSchema.methods.generatePasswordResetToken = function(): string {\r\n  const crypto = require('crypto');\r\n  const token = crypto.randomBytes(32).toString('hex');\r\n  \r\n  this.passwordResetToken = crypto.createHash('sha256').update(token).digest('hex');\r\n  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\r\n  \r\n  return token;\r\n};\r\n\r\n// Instance method to calculate profile completion\r\nUserSchema.methods.calculateProfileCompletion = function(): number {\r\n  let score = 0;\r\n  \r\n  // Basic required fields (10 points each)\r\n  if (this.firstName) score += 10;\r\n  if (this.lastName) score += 10;\r\n  if (this.email) score += 10;\r\n  if (this.dateOfBirth) score += 10;\r\n  if (this.gender) score += 10;\r\n  \r\n  // Optional but important fields (10 points each)\r\n  if (this.phoneNumber) score += 10;\r\n  if (this.location?.city) score += 10;\r\n  if (this.location?.state) score += 10;\r\n  \r\n  // Verification status (10 points each)\r\n  if (this.isEmailVerified) score += 10;\r\n  if (this.isPhoneVerified) score += 10;\r\n  \r\n  return Math.min(score, 100);\r\n};\r\n\r\n// Instance method to get full name\r\nUserSchema.methods.getFullName = function(): string {\r\n  return `${this.firstName} ${this.lastName}`;\r\n};\r\n\r\n// Instance method to get age\r\nUserSchema.methods.getAge = function(): number {\r\n  const today = new Date();\r\n  const birthDate = new Date(this.dateOfBirth);\r\n  let age = today.getFullYear() - birthDate.getFullYear();\r\n  const monthDiff = today.getMonth() - birthDate.getMonth();\r\n  \r\n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\r\n    age--;\r\n  }\r\n  \r\n  return age;\r\n};\r\n\r\n// Export the model\r\nexport const User = mongoose.model<IUser>('User', UserSchema);\r\nexport default User;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,UAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,YAAA,CAAAC,OAAA;AACA,MAAAC,UAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAAI,eAAA,CAAAF,OAAA;AA+DA;AACA,MAAMG,UAAU;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,QAAG,IAAIF,UAAA,CAAAQ,MAAM,CAAQ;EACnC;EACAC,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC;IACrCC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,CACL,kDAAkD,EAClD,sCAAsC,CACvC;IACDC,KAAK,EAAE;GACR;EAEDC,QAAQ,EAAE;IACRR,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;IACxCO,SAAS,EAAE,CAAC,CAAC,EAAE,6CAA6C,CAAC;IAC7DC,MAAM,EAAE,KAAK,CAAC;GACf;EAEDC,SAAS,EAAE;IACTX,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,CAAC,IAAI,EAAE,wBAAwB,CAAC;IAC1CG,IAAI,EAAE,IAAI;IACVI,SAAS,EAAE,CAAC,CAAC,EAAE,0CAA0C,CAAC;IAC1DG,SAAS,EAAE,CAAC,EAAE,EAAE,wCAAwC;GACzD;EAEDC,QAAQ,EAAE;IACRb,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,CAAC,IAAI,EAAE,uBAAuB,CAAC;IACzCG,IAAI,EAAE,IAAI;IACVI,SAAS,EAAE,CAAC,CAAC,EAAE,yCAAyC,CAAC;IACzDG,SAAS,EAAE,CAAC,EAAE,EAAE,uCAAuC;GACxD;EAEDE,WAAW,EAAE;IACXd,IAAI,EAAEe,IAAI;IACVb,QAAQ,EAAE,CAAC,IAAI,EAAE,2BAA2B,CAAC;IAC7Cc,QAAQ,EAAE;MACRC,SAAS,EAAE,SAAAA,CAASC,KAAW;QAAA;QAAA3B,cAAA,GAAA4B,CAAA;QAC7B,MAAMC,GAAG;QAAA;QAAA,CAAA7B,cAAA,GAAAC,CAAA,QAAG,IAAIuB,IAAI,EAAE,CAACM,WAAW,EAAE,GAAGH,KAAK,CAACG,WAAW,EAAE;QAAC;QAAA9B,cAAA,GAAAC,CAAA;QAC3D,OAAO,2BAAAD,cAAA,GAAA+B,CAAA,WAAAF,GAAG,IAAI,EAAE;QAAA;QAAA,CAAA7B,cAAA,GAAA+B,CAAA,WAAIF,GAAG,IAAI,GAAG;MAChC,CAAC;MACDG,OAAO,EAAE;;GAEZ;EAEDC,MAAM,EAAE;IACNxB,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,CAAC,IAAI,EAAE,oBAAoB,CAAC;IACtCuB,IAAI,EAAE;MACJC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,mBAAmB,CAAC;MAC7DH,OAAO,EAAE;;GAEZ;EAEDI,WAAW,EAAE;IACX3B,IAAI,EAAEC,MAAM;IACZI,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,CAAC,oBAAoB,EAAE,qCAAqC,CAAC;IACpEsB,MAAM,EAAE,IAAI,CAAC;GACd;EAED;EACAC,eAAe,EAAE;IACf7B,IAAI,EAAE8B,OAAO;IACbC,OAAO,EAAE;GACV;EAEDC,eAAe,EAAE;IACfhC,IAAI,EAAE8B,OAAO;IACbC,OAAO,EAAE;GACV;EAEDE,QAAQ,EAAE;IACRjC,IAAI,EAAE8B,OAAO;IACbC,OAAO,EAAE;GACV;EAEDG,WAAW,EAAE;IACXlC,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,CAAC,IAAI,EAAE,0BAA0B,CAAC;IAC5CuB,IAAI,EAAE;MACJC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;MACnCH,OAAO,EAAE;KACV;IACDQ,OAAO,EAAE;GACV;EAED;EACAI,sBAAsB,EAAE;IACtBnC,IAAI,EAAEC,MAAM;IACZS,MAAM,EAAE;GACT;EAED0B,wBAAwB,EAAE;IACxBpC,IAAI,EAAEe,IAAI;IACVL,MAAM,EAAE;GACT;EAED2B,kBAAkB,EAAE;IAClBrC,IAAI,EAAEC,MAAM;IACZS,MAAM,EAAE;GACT;EAED4B,oBAAoB,EAAE;IACpBtC,IAAI,EAAEe,IAAI;IACVL,MAAM,EAAE;GACT;EAED6B,aAAa,EAAE,CAAC;IACdvC,IAAI,EAAEC,MAAM;IACZS,MAAM,EAAE;GACT,CAAC;EAEF;EACA8B,sBAAsB,EAAE;IACtBxC,IAAI,EAAEyC,MAAM;IACZV,OAAO,EAAE,CAAC;IACVW,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE;GACN;EAEDC,WAAW,EAAE;IACX5C,IAAI,EAAEe;GACP;EAED8B,YAAY,EAAE;IACZ7C,IAAI,EAAEe,IAAI;IACVgB,OAAO,EAAEhB,IAAI,CAAC+B;GACf;EAED;EACAC,WAAW,EAAE;IACXC,kBAAkB,EAAE;MAClBhD,IAAI,EAAE8B,OAAO;MACbC,OAAO,EAAE;KACV;IACDkB,iBAAiB,EAAE;MACjBjD,IAAI,EAAE8B,OAAO;MACbC,OAAO,EAAE;KACV;IACDmB,gBAAgB,EAAE;MAChBlD,IAAI,EAAE8B,OAAO;MACbC,OAAO,EAAE;KACV;IACDoB,eAAe,EAAE;MACfnD,IAAI,EAAE8B,OAAO;MACbC,OAAO,EAAE;;GAEZ;EAED;EACAqB,QAAQ,EAAE;IACRC,IAAI,EAAE;MACJrD,IAAI,EAAEC,MAAM;MACZI,IAAI,EAAE;KACP;IACDiD,KAAK,EAAE;MACLtD,IAAI,EAAEC,MAAM;MACZI,IAAI,EAAE;KACP;IACDkD,OAAO,EAAE;MACPvD,IAAI,EAAEC,MAAM;MACZI,IAAI,EAAE,IAAI;MACV0B,OAAO,EAAE;KACV;IACDyB,WAAW,EAAE;MACXxD,IAAI,EAAE;QACJA,IAAI,EAAEC,MAAM;QACZwB,IAAI,EAAE,CAAC,OAAO,CAAC;QACfvB,QAAQ,EAAE,SAAAA,CAAA;UAAA;UAAAX,cAAA,GAAA4B,CAAA;UAAA5B,cAAA,GAAAC,CAAA;UACR,OAAO,2BAAAD,cAAA,GAAA+B,CAAA,eAAI,CAACkC,WAAW;UAAA;UAAA,CAAAjE,cAAA,GAAA+B,CAAA,WAAI,IAAI,CAACkC,WAAW,CAACA,WAAW;QACzD;OACD;MACDA,WAAW,EAAE;QACXxD,IAAI,EAAE,CAACyC,MAAM,CAAC;QAAE;QAChBzB,QAAQ,EAAE;UACRC,SAAS,EAAE,SAAAA,CAASwC,MAAgB;YAAA;YAAAlE,cAAA,GAAA4B,CAAA;YAAA5B,cAAA,GAAAC,CAAA;YAClC,OAAO,2BAAAD,cAAA,GAAA+B,CAAA,YAACmC,MAAM;YAAK;YAAA,CAAAlE,cAAA,GAAA+B,CAAA,WAAAmC,MAAM,CAACC,MAAM,KAAK,CAAC;YAAA;YAAA,CAAAnE,cAAA,GAAA+B,CAAA,WAC/BmC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;YAAA;YAAA,CAAAlE,cAAA,GAAA+B,CAAA,WAAImC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG;YAAA;YAAA,CAAAlE,cAAA,GAAA+B,CAAA;YAAI;YACzCmC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;YAAA;YAAA,CAAAlE,cAAA,GAAA+B,CAAA,WAAImC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAK;UACnD,CAAC;UACDlC,OAAO,EAAE;;;;;CAKlB,EAAE;EACDoC,UAAU,EAAE,IAAI;EAChBC,MAAM,EAAE;IACNC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,SAAAA,CAASC,IAAI,EAAEC,GAAG;MAAA;MAAAzE,cAAA,GAAA4B,CAAA;MAAA5B,cAAA,GAAAC,CAAA;MAC3B,OAAOwE,GAAG,CAACxD,QAAQ;MAAC;MAAAjB,cAAA,GAAAC,CAAA;MACpB,OAAOwE,GAAG,CAACzB,aAAa;MAAC;MAAAhD,cAAA,GAAAC,CAAA;MACzB,OAAOwE,GAAG,CAAC7B,sBAAsB;MAAC;MAAA5C,cAAA,GAAAC,CAAA;MAClC,OAAOwE,GAAG,CAAC3B,kBAAkB;MAAC;MAAA9C,cAAA,GAAAC,CAAA;MAC9B,OAAOwE,GAAG;IACZ;GACD;EACDC,QAAQ,EAAE;IAAEJ,QAAQ,EAAE;EAAI;CAC3B,CAAC;AAEF;AAAA;AAAAtE,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACU,KAAK,CAAC;EAAER,KAAK,EAAE;AAAC,CAAE,EAAE;EAAEI,MAAM,EAAE;AAAI,CAAE,CAAC;AAAC;AAAAZ,cAAA,GAAAC,CAAA;AACjDK,UAAU,CAACU,KAAK,CAAC;EAAE,sBAAsB,EAAE;AAAU,CAAE,EAAE;EAAEqB,MAAM,EAAE;AAAI,CAAE,CAAC;AAAC;AAAArC,cAAA,GAAAC,CAAA;AAC3EK,UAAU,CAACU,KAAK,CAAC;EAAE2B,WAAW,EAAE;AAAC,CAAE,CAAC;AAAC;AAAA3C,cAAA,GAAAC,CAAA;AACrCK,UAAU,CAACU,KAAK,CAAC;EAAE0B,QAAQ,EAAE;AAAC,CAAE,CAAC;AAAC;AAAA1C,cAAA,GAAAC,CAAA;AAClCK,UAAU,CAACU,KAAK,CAAC;EAAE2D,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAA3E,cAAA,GAAAC,CAAA;AACpCK,UAAU,CAACU,KAAK,CAAC;EAAEsC,YAAY,EAAE,CAAC;AAAC,CAAE,CAAC;AAEtC;AAAA;AAAAtD,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACsE,OAAO,CAAC,UAAU,CAAC,CAACC,GAAG,CAAC;EAAA;EAAA7E,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EACjC,OAAO,GAAG,IAAI,CAACmB,SAAS,IAAI,IAAI,CAACE,QAAQ,EAAE;AAC7C,CAAC,CAAC;AAEF;AAAA;AAAAtB,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACsE,OAAO,CAAC,KAAK,CAAC,CAACC,GAAG,CAAC;EAAA;EAAA7E,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAC5B,OAAO,IAAIuB,IAAI,EAAE,CAACM,WAAW,EAAE,GAAG,IAAI,CAACP,WAAW,CAACO,WAAW,EAAE;AAClE,CAAC,CAAC;AAEF;AAAA;AAAA9B,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACwE,GAAG,CAAC,MAAM,EAAE,gBAAeC,IAAI;EAAA;EAAA/E,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EACxC;EACA,IAAI,CAAC,IAAI,CAAC+E,UAAU,CAAC,UAAU,CAAC,EAAE;IAAA;IAAAhF,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAA,OAAO8E,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAA/E,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAEhD,IAAI;IACF;IACA,MAAMgF,IAAI;IAAA;IAAA,CAAAjF,cAAA,GAAAC,CAAA,QAAG,MAAMG,UAAA,CAAAoC,OAAM,CAAC0C,OAAO,CAAC,EAAE,CAAC;IAAC;IAAAlF,cAAA,GAAAC,CAAA;IACtC,IAAI,CAACgB,QAAQ,GAAG,MAAMb,UAAA,CAAAoC,OAAM,CAAC2C,IAAI,CAAC,IAAI,CAAClE,QAAQ,EAAEgE,IAAI,CAAC;IAAC;IAAAjF,cAAA,GAAAC,CAAA;IACvD8E,IAAI,EAAE;EACR,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA;IAAApF,cAAA,GAAAC,CAAA;IACd8E,IAAI,CAACK,KAAc,CAAC;EACtB;AACF,CAAC,CAAC;AAEF;AAAA;AAAApF,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACwE,GAAG,CAAC,MAAM,EAAE,UAASC,IAAI;EAAA;EAAA/E,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAClC,IAAI,CAACgD,sBAAsB,GAAG,IAAI,CAACoC,0BAA0B,EAAE;EAAC;EAAArF,cAAA,GAAAC,CAAA;EAChE8E,IAAI,EAAE;AACR,CAAC,CAAC;AAEF;AAAA;AAAA/E,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACgF,OAAO,CAACC,eAAe,GAAG,gBAAeC,iBAAyB;EAAA;EAAAxF,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAC3E,OAAOG,UAAA,CAAAoC,OAAM,CAACiD,OAAO,CAACD,iBAAiB,EAAE,IAAI,CAACvE,QAAQ,CAAC;AACzD,CAAC;AAED;AAAA;AAAAjB,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACgF,OAAO,CAACI,8BAA8B,GAAG;EAAA;EAAA1F,cAAA,GAAA4B,CAAA;EAClD,MAAM+D,MAAM;EAAA;EAAA,CAAA3F,cAAA,GAAAC,CAAA,QAAGE,OAAO,CAAC,QAAQ,CAAC;EAChC,MAAMyF,KAAK;EAAA;EAAA,CAAA5F,cAAA,GAAAC,CAAA,QAAG0F,MAAM,CAACE,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;EAAC;EAAA9F,cAAA,GAAAC,CAAA;EAErD,IAAI,CAAC2C,sBAAsB,GAAG+C,MAAM,CAACI,UAAU,CAAC,QAAQ,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC,CAACK,MAAM,CAAC,KAAK,CAAC;EAAC;EAAAjG,cAAA,GAAAC,CAAA;EACtF,IAAI,CAAC4C,wBAAwB,GAAG,IAAIrB,IAAI,CAACA,IAAI,CAAC+B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;EAAA;EAAAvD,cAAA,GAAAC,CAAA;EAE5E,OAAO2F,KAAK;AACd,CAAC;AAED;AAAA;AAAA5F,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACgF,OAAO,CAACY,0BAA0B,GAAG;EAAA;EAAAlG,cAAA,GAAA4B,CAAA;EAC9C,MAAM+D,MAAM;EAAA;EAAA,CAAA3F,cAAA,GAAAC,CAAA,QAAGE,OAAO,CAAC,QAAQ,CAAC;EAChC,MAAMyF,KAAK;EAAA;EAAA,CAAA5F,cAAA,GAAAC,CAAA,QAAG0F,MAAM,CAACE,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;EAAC;EAAA9F,cAAA,GAAAC,CAAA;EAErD,IAAI,CAAC6C,kBAAkB,GAAG6C,MAAM,CAACI,UAAU,CAAC,QAAQ,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC,CAACK,MAAM,CAAC,KAAK,CAAC;EAAC;EAAAjG,cAAA,GAAAC,CAAA;EAClF,IAAI,CAAC8C,oBAAoB,GAAG,IAAIvB,IAAI,CAACA,IAAI,CAAC+B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;EAAA;EAAAvD,cAAA,GAAAC,CAAA;EAEnE,OAAO2F,KAAK;AACd,CAAC;AAED;AAAA;AAAA5F,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACgF,OAAO,CAACD,0BAA0B,GAAG;EAAA;EAAArF,cAAA,GAAA4B,CAAA;EAC9C,IAAIuE,KAAK;EAAA;EAAA,CAAAnG,cAAA,GAAAC,CAAA,QAAG,CAAC;EAEb;EAAA;EAAAD,cAAA,GAAAC,CAAA;EACA,IAAI,IAAI,CAACmB,SAAS,EAAE;IAAA;IAAApB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAChC,IAAI,IAAI,CAACqB,QAAQ,EAAE;IAAA;IAAAtB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAC/B,IAAI,IAAI,CAACO,KAAK,EAAE;IAAA;IAAAR,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAC5B,IAAI,IAAI,CAACsB,WAAW,EAAE;IAAA;IAAAvB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAClC,IAAI,IAAI,CAACgC,MAAM,EAAE;IAAA;IAAAjC,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAE7B;EAAA/B,cAAA,GAAAC,CAAA;EACA,IAAI,IAAI,CAACmC,WAAW,EAAE;IAAA;IAAApC,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAClC,IAAI,IAAI,CAAC4D,QAAQ,EAAEC,IAAI,EAAE;IAAA;IAAA9D,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EACrC,IAAI,IAAI,CAAC4D,QAAQ,EAAEE,KAAK,EAAE;IAAA;IAAA/D,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAEtC;EAAA/B,cAAA,GAAAC,CAAA;EACA,IAAI,IAAI,CAACqC,eAAe,EAAE;IAAA;IAAAtC,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EACtC,IAAI,IAAI,CAACwC,eAAe,EAAE;IAAA;IAAAzC,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAAAkG,KAAK,IAAI,EAAE;EAAA,CAAC;EAAA;EAAA;IAAAnG,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAEtC,OAAOmG,IAAI,CAACjD,GAAG,CAACgD,KAAK,EAAE,GAAG,CAAC;AAC7B,CAAC;AAED;AAAA;AAAAnG,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACgF,OAAO,CAACe,WAAW,GAAG;EAAA;EAAArG,cAAA,GAAA4B,CAAA;EAAA5B,cAAA,GAAAC,CAAA;EAC/B,OAAO,GAAG,IAAI,CAACmB,SAAS,IAAI,IAAI,CAACE,QAAQ,EAAE;AAC7C,CAAC;AAED;AAAA;AAAAtB,cAAA,GAAAC,CAAA;AACAK,UAAU,CAACgF,OAAO,CAACgB,MAAM,GAAG;EAAA;EAAAtG,cAAA,GAAA4B,CAAA;EAC1B,MAAM2E,KAAK;EAAA;EAAA,CAAAvG,cAAA,GAAAC,CAAA,SAAG,IAAIuB,IAAI,EAAE;EACxB,MAAMgF,SAAS;EAAA;EAAA,CAAAxG,cAAA,GAAAC,CAAA,SAAG,IAAIuB,IAAI,CAAC,IAAI,CAACD,WAAW,CAAC;EAC5C,IAAIM,GAAG;EAAA;EAAA,CAAA7B,cAAA,GAAAC,CAAA,SAAGsG,KAAK,CAACzE,WAAW,EAAE,GAAG0E,SAAS,CAAC1E,WAAW,EAAE;EACvD,MAAM2E,SAAS;EAAA;EAAA,CAAAzG,cAAA,GAAAC,CAAA,SAAGsG,KAAK,CAACG,QAAQ,EAAE,GAAGF,SAAS,CAACE,QAAQ,EAAE;EAAC;EAAA1G,cAAA,GAAAC,CAAA;EAE1D;EAAI;EAAA,CAAAD,cAAA,GAAA+B,CAAA,WAAA0E,SAAS,GAAG,CAAC;EAAK;EAAA,CAAAzG,cAAA,GAAA+B,CAAA,WAAA0E,SAAS,KAAK,CAAC;EAAA;EAAA,CAAAzG,cAAA,GAAA+B,CAAA,WAAIwE,KAAK,CAACI,OAAO,EAAE,GAAGH,SAAS,CAACG,OAAO,EAAE,CAAC,EAAE;IAAA;IAAA3G,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAC,CAAA;IAC/E4B,GAAG,EAAE;EACP,CAAC;EAAA;EAAA;IAAA7B,cAAA,GAAA+B,CAAA;EAAA;EAAA/B,cAAA,GAAAC,CAAA;EAED,OAAO4B,GAAG;AACZ,CAAC;AAED;AAAA;AAAA7B,cAAA,GAAAC,CAAA;AACa2G,OAAA,CAAAC,IAAI,GAAG9G,UAAA,CAAAyC,OAAQ,CAACsE,KAAK,CAAQ,MAAM,EAAExG,UAAU,CAAC;AAAC;AAAAN,cAAA,GAAAC,CAAA;AAC9D2G,OAAA,CAAApE,OAAA,GAAeoE,OAAA,CAAAC,IAAI", "ignoreList": []}