import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

// User interface
export interface IUser extends Document {
  // Basic Information
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  gender: 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
  phoneNumber?: string;

  // Account Status
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isActive: boolean;
  accountType: 'seeker' | 'owner' | 'both';

  // Authentication
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  refreshTokens: string[];

  // Profile Completion
  profileCompletionScore: number;
  lastLoginAt?: Date;
  lastActiveAt: Date;

  // Preferences
  preferences: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    smsNotifications: boolean;
    marketingEmails: boolean;
  };

  // Location
  location?: {
    city: string;
    state: string;
    country: string;
    coordinates?: {
      type: 'Point';
      coordinates: [number, number]; // [longitude, latitude]
    };
  };

  // Timestamps
  createdAt: Date;
  updatedAt: Date;

  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateEmailVerificationToken(): string;
  generatePasswordResetToken(): string;
  calculateProfileCompletion(): number;
  getFullName(): string;
  getAge(): number;
}

// User Schema
const UserSchema = new Schema<IUser>({
  // Basic Information
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please provide a valid email address'
    ],
    index: true
  },
  
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false // Don't include password in queries by default
  },
  
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    minlength: [2, 'First name must be at least 2 characters'],
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    minlength: [2, 'Last name must be at least 2 characters'],
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  
  dateOfBirth: {
    type: Date,
    required: [true, 'Date of birth is required'],
    validate: {
      validator: function(value: Date) {
        const age = new Date().getFullYear() - value.getFullYear();
        return age >= 18 && age <= 100;
      },
      message: 'You must be between 18 and 100 years old'
    }
  },
  
  gender: {
    type: String,
    required: [true, 'Gender is required'],
    enum: {
      values: ['male', 'female', 'non-binary', 'prefer-not-to-say'],
      message: 'Gender must be one of: male, female, non-binary, prefer-not-to-say'
    }
  },
  
  phoneNumber: {
    type: String,
    trim: true,
    match: [/^\+?[\d\s\-\(\)]+$/, 'Please provide a valid phone number'],
    sparse: true // Allow multiple null values
  },

  // Account Status
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  
  isPhoneVerified: {
    type: Boolean,
    default: false
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  accountType: {
    type: String,
    required: [true, 'Account type is required'],
    enum: {
      values: ['seeker', 'owner', 'both'],
      message: 'Account type must be one of: seeker, owner, both'
    },
    default: 'seeker'
  },

  // Authentication
  emailVerificationToken: {
    type: String,
    select: false
  },
  
  emailVerificationExpires: {
    type: Date,
    select: false
  },
  
  passwordResetToken: {
    type: String,
    select: false
  },
  
  passwordResetExpires: {
    type: Date,
    select: false
  },
  
  refreshTokens: [{
    type: String,
    select: false
  }],

  // Profile Completion
  profileCompletionScore: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  lastLoginAt: {
    type: Date
  },
  
  lastActiveAt: {
    type: Date,
    default: Date.now
  },

  // Preferences
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    pushNotifications: {
      type: Boolean,
      default: true
    },
    smsNotifications: {
      type: Boolean,
      default: false
    },
    marketingEmails: {
      type: Boolean,
      default: false
    }
  },

  // Location
  location: {
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true,
      default: 'Nigeria'
    },
    coordinates: {
      type: {
        type: String,
        enum: ['Point'],
        required: function() {
          return this.coordinates && this.coordinates.coordinates;
        }
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        validate: {
          validator: function(coords: number[]) {
            return !coords || (coords.length === 2 &&
                   coords[0] >= -180 && coords[0] <= 180 && // longitude
                   coords[1] >= -90 && coords[1] <= 90);     // latitude
          },
          message: 'Coordinates must be [longitude, latitude] with valid ranges'
        }
      }
    }
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(_doc, ret) {
      delete ret.password;
      delete ret.refreshTokens;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes for performance
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ 'location.coordinates': '2dsphere' }, { sparse: true });
UserSchema.index({ accountType: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ lastActiveAt: -1 });

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for age
UserSchema.virtual('age').get(function() {
  return new Date().getFullYear() - this.dateOfBirth.getFullYear();
});

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  // Only hash password if it's modified
  if (!this.isModified('password')) return next();
  
  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Pre-save middleware to calculate profile completion
UserSchema.pre('save', function(next) {
  this.profileCompletionScore = this.calculateProfileCompletion();
  next();
});

// Instance method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Instance method to generate email verification token
UserSchema.methods.generateEmailVerificationToken = function(): string {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  
  this.emailVerificationToken = crypto.createHash('sha256').update(token).digest('hex');
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  
  return token;
};

// Instance method to generate password reset token
UserSchema.methods.generatePasswordResetToken = function(): string {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  
  this.passwordResetToken = crypto.createHash('sha256').update(token).digest('hex');
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  
  return token;
};

// Instance method to calculate profile completion
UserSchema.methods.calculateProfileCompletion = function(): number {
  let score = 0;
  
  // Basic required fields (10 points each)
  if (this.firstName) score += 10;
  if (this.lastName) score += 10;
  if (this.email) score += 10;
  if (this.dateOfBirth) score += 10;
  if (this.gender) score += 10;
  
  // Optional but important fields (10 points each)
  if (this.phoneNumber) score += 10;
  if (this.location?.city) score += 10;
  if (this.location?.state) score += 10;
  
  // Verification status (10 points each)
  if (this.isEmailVerified) score += 10;
  if (this.isPhoneVerified) score += 10;
  
  return Math.min(score, 100);
};

// Instance method to get full name
UserSchema.methods.getFullName = function(): string {
  return `${this.firstName} ${this.lastName}`;
};

// Instance method to get age
UserSchema.methods.getAge = function(): number {
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

// Export the model
export const User = mongoose.model<IUser>('User', UserSchema);
export default User;
